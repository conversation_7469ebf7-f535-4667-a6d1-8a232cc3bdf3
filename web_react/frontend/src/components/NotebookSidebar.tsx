import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  Title,
  ActionIcon,
  Text,
  Badge,
  Stack,
  Group,
  Divider,
  Button,
  Modal,
  Select,
  Tooltip,
  ColorInput,
  Popover,
  TextInput
} from '@mantine/core';
import {
  IconNotebook,
  IconChevronRight,
  IconChevronLeft,
  IconTrash,
  IconDeviceFloppy,
  IconGripVertical,
  IconList,
  IconTypography,
  IconTextSize,
  IconBold,
  IconHighlight,
  IconBookmark,
  IconPalette
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import { api, notebookApi } from '../services/api';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../store/useAuthStore';
import { useThemeColors, useThemeStore } from '../store/useThemeStore';

interface NotebookSidebarProps {
  className?: string;
  isExpanded?: boolean;
}

export const NotebookSidebar: React.FC<NotebookSidebarProps> = ({
  className = '',
  isExpanded = true
}) => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const themeColors = useThemeColors();
  const { resolvedTheme } = useThemeStore();
  const [notes, setNotes] = useState('');
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [deleteModalOpened, setDeleteModalOpened] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(260);
  const [isResizing, setIsResizing] = useState(false);
  const [fontSize, setFontSize] = useState('14');
  const [selectedColor, setSelectedColor] = useState('#000000');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isOnlineMode, setIsOnlineMode] = useState(false);

  // Save notebook state
  const [saveModalOpened, { open: openSaveModal, close: closeSaveModal }] = useDisclosure(false);
  const [notebookName, setNotebookName] = useState('');
  const [isSavingNotebook, setIsSavingNotebook] = useState(false);

  // Formatting state tracking
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isHighlightActiveState, setIsHighlightActiveState] = useState(false);
  const [isColorActive, setIsColorActive] = useState(false);
  
  const autoSaveTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const resizeRef = useRef<HTMLDivElement>(null);

  // Generate user-specific localStorage keys
  const getUserSpecificKey = useCallback((baseKey: string) => {
    if (user?.id) {
      return `${baseKey}-${user.id}`;
    }
    return `${baseKey}-anonymous`;
  }, [user?.id]);

  // Load notes from database and settings from localStorage on component mount
  useEffect(() => {
    const loadNotesFromDatabase = async () => {
      // Ensure user is available before attempting database operations
      if (!user?.id) {
        setIsLoading(false);
        setIsOnlineMode(false);
        return;
      }

      try {
        setIsLoading(true);
        
        // Always try to load from database first
        const response = await api.get('/user/notebook');
        const notesContent = response.data.notes || '';
        
        // Always use database content if available
        if (notesContent) {

          setNotes(notesContent);
          
          // Use setTimeout to ensure DOM is ready (like the manual fix that worked)
          setTimeout(() => {
            if (editorRef.current) {
              editorRef.current.innerHTML = notesContent;

              
              // Trigger input event to sync component state
              const event = new Event('input', { bubbles: true });
              editorRef.current.dispatchEvent(event);
            }
          }, 100);
          
          setIsOnlineMode(true);
        } else {
          // Only check localStorage if database is empty
          const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
          const localNotes = localStorage.getItem(userSpecificNotesKey);
          
          if (localNotes && localNotes.trim()) {
            try {
              // Migrate localStorage notes to database
              await api.post('/user/notebook', { notes: localNotes });
              setNotes(localNotes);
              if (editorRef.current) {
                setTimeout(() => { if (editorRef.current) { editorRef.current.innerHTML = localNotes; const event = new Event("input", { bubbles: true }); editorRef.current.dispatchEvent(event); } }, 100);
              }
              setIsOnlineMode(true);
              
              notifications.show({
                title: 'Notes migrées',
                message: 'Vos notes locales ont été synchronisées avec votre compte',
                color: 'green',
                autoClose: 3000,
              });
            } catch (migrationError) {
              console.error('Failed to migrate notes:', migrationError);
              // Use localStorage notes even if migration fails
              setNotes(localNotes);
              if (editorRef.current) {
                setTimeout(() => { if (editorRef.current) { editorRef.current.innerHTML = localNotes; const event = new Event("input", { bubbles: true }); editorRef.current.dispatchEvent(event); } }, 100);
              }
              setIsOnlineMode(false);
            }
          } else {
            // No notes anywhere - start fresh
            setNotes('');
            if (editorRef.current) {
            }
            setIsOnlineMode(true);
          }
        }
        
        // Load saved timestamp from localStorage
        const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
        const savedTimestamp = localStorage.getItem(userSpecificTimestampKey);
        if (savedTimestamp) {
          setLastSaved(new Date(savedTimestamp));
        }
        
        
      } catch (error: any) {
        console.error('Error loading notes from database:', error);
        
        // Handle authentication errors specifically
        if (error.response?.status === 401) {
          setIsOnlineMode(false);
          
          notifications.show({
            title: 'Session en cours de restauration',
            message: 'Vos notes seront synchronisées une fois la session restaurée',
            color: 'blue',
            autoClose: 4000,
          });
        } else {
          // Other errors - fallback to localStorage
          setIsOnlineMode(false);
          
          notifications.show({
            title: 'Mode hors ligne temporaire',
            message: 'Connexion en cours... Vos notes sont sauvegardées localement.',
            color: 'yellow',
            autoClose: 3000,
          });
        }
        
        // Fallback to user-specific localStorage if database fails
        const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
        const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
        const savedNotes = localStorage.getItem(userSpecificNotesKey);
        const savedTimestamp = localStorage.getItem(userSpecificTimestampKey);
        
        if (savedNotes) {
          setNotes(savedNotes);
          if (editorRef.current) {
            setTimeout(() => { if (editorRef.current) { editorRef.current.innerHTML = savedNotes; const event = new Event("input", { bubbles: true }); editorRef.current.dispatchEvent(event); } }, 100);
          }
          if (savedTimestamp) {
            setLastSaved(new Date(savedTimestamp));
          }
        } else {
          // No fallback notes available
          setNotes('');
          if (editorRef.current) {
          }
        }
      } finally {
        setIsLoading(false);
      }
    };

    // Load UI preferences from localStorage
    const savedWidth = localStorage.getItem('tcf-notebook-width');
    const savedFontSize = localStorage.getItem('tcf-notebook-fontsize');
    const savedColor = localStorage.getItem('tcf-notebook-color');
    
    if (savedWidth) {
      setSidebarWidth(parseInt(savedWidth));
    }
    
    if (savedFontSize) {
      setFontSize(savedFontSize);
    }
    
    if (savedColor) {
      setSelectedColor(savedColor);
    }

    // Load notes from database
    loadNotesFromDatabase();
  }, [getUserSpecificKey, user?.id]);

  // Reload notes when user authentication state changes (session restoration)
  useEffect(() => {
    if (user?.id && !isLoading && !isOnlineMode) {
      const reloadNotesAfterAuth = async () => {
        try {
          setIsLoading(true);
          const response = await api.get('/user/notebook');
          const notesContent = response.data.notes || '';
          
          if (notesContent) {
            setNotes(notesContent);
            if (editorRef.current) {
              setTimeout(() => { if (editorRef.current) { editorRef.current.innerHTML = notesContent; const event = new Event("input", { bubbles: true }); editorRef.current.dispatchEvent(event); } }, 100);
            }
            setIsOnlineMode(true);
          }
        } catch (error) {
          console.error('🔍 NotebookSidebar: Failed to reload notes after session restoration:', error);
        } finally {
          setIsLoading(false);
        }
      };
      
      reloadNotesAfterAuth();
    }
  }, [user?.id, isOnlineMode]);

  // Auto-save function - now saves to database
  const saveNotes = useCallback(async (notesContent: string) => {
    try {
      setIsSaving(true);
      
      // Save to database
      await api.post('/user/notebook', { notes: notesContent });
      
      const timestamp = new Date();
      setLastSaved(timestamp);
      setHasUnsavedChanges(false);
      setIsOnlineMode(true);
      
      // Also keep a user-specific backup in localStorage
      const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
      const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
      localStorage.setItem(userSpecificNotesKey, notesContent);
      localStorage.setItem(userSpecificTimestampKey, timestamp.toISOString());
      
    } catch (error: any) {
      console.error('Failed to save notes to database:', error);
      
      // Handle specific error cases
      if (error.response?.data?.code === 'USER_NOT_FOUND' || 
          error.response?.data?.code === 'USER_ACCOUNT_ERROR' ||
          error.response?.data?.code === 'RLS_CONSTRAINT_ERROR' ||
          error.response?.data?.code === 'CONSTRAINT_ERROR' ||
          error.response?.data?.code === 'DATABASE_ACCESS_ERROR') {
        
        // If it's a constraint or database access error, don't try to fix account
        if (error.response?.data?.code === 'RLS_CONSTRAINT_ERROR' ||
            error.response?.data?.code === 'CONSTRAINT_ERROR' ||
            error.response?.data?.code === 'DATABASE_ACCESS_ERROR') {
          setIsOnlineMode(false);
          
          notifications.show({
            title: 'Mode hors ligne',
            message: 'Problème de base de données. Vos notes sont sauvegardées localement.',
            color: 'yellow',
            autoClose: 4000,
          });
          
          // Save to user-specific localStorage as fallback
          const timestamp = new Date();
          const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
          const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
          localStorage.setItem(userSpecificNotesKey, notesContent);
          localStorage.setItem(userSpecificTimestampKey, timestamp.toISOString());
          setLastSaved(timestamp);
          setHasUnsavedChanges(false);
          
          return;
        }
        
        // Try to fix the account automatically for other errors
        try {
          await api.post('/user/fix-account');

          // If account fix succeeded, try saving again
          await api.post('/user/notebook', { notes: notesContent });
          
          const timestamp = new Date();
          setLastSaved(timestamp);
          setHasUnsavedChanges(false);
          setIsOnlineMode(true);
          
          // Also keep a user-specific backup in localStorage
          const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
          const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
          localStorage.setItem(userSpecificNotesKey, notesContent);
          localStorage.setItem(userSpecificTimestampKey, timestamp.toISOString());
          
          notifications.show({
            title: 'Compte réparé',
            message: 'Votre compte a été réparé et vos notes synchronisées.',
            color: 'green',
            autoClose: 3000,
          });
          
          return; // Success after fixing
          
        } catch (fixError) {
          console.error('Failed to fix account:', fixError);
          // Fall through to local storage backup
        }
        
        // Critical auth error - user needs to re-authenticate
        setIsOnlineMode(false);
        
        notifications.show({
          title: 'Sauvegarde locale activée',
          message: 'Vos notes sont sauvegardées localement. La synchronisation sera rétablie après reconnexion.',
          color: 'orange',
          autoClose: 4000,
        });
        
        // Save to user-specific localStorage as fallback
        const timestamp = new Date();
        const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
        const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
        localStorage.setItem(userSpecificNotesKey, notesContent);
        localStorage.setItem(userSpecificTimestampKey, timestamp.toISOString());
        setLastSaved(timestamp);
        setHasUnsavedChanges(false);
        
        return;
      }
      
      // Also check for generic foreign key constraint errors
      if (error.response?.data?.error?.includes('foreign key constraint') || 
          error.response?.status === 400) {
        // Database constraint issue - fall back to user-specific local storage
        setIsOnlineMode(false);
        
        notifications.show({
          title: 'Mode hors ligne',
          message: 'Notes sauvegardées localement. Reconnectez-vous pour synchroniser.',
          color: 'yellow',
          autoClose: 3000,
        });
        
        // Save to user-specific localStorage as fallback
        const timestamp = new Date();
        const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
        const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
        localStorage.setItem(userSpecificNotesKey, notesContent);
        localStorage.setItem(userSpecificTimestampKey, timestamp.toISOString());
        setLastSaved(timestamp);
        setHasUnsavedChanges(false);
        
        return;
      }
      
      // Fallback to user-specific localStorage for other errors
      const timestamp = new Date();
      const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
      const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
      localStorage.setItem(userSpecificNotesKey, notesContent);
      localStorage.setItem(userSpecificTimestampKey, timestamp.toISOString());
      setLastSaved(timestamp);
      setHasUnsavedChanges(false);
      setIsOnlineMode(false);
      
      notifications.show({
        title: 'Sauvegarde locale',
        message: 'Notes sauvegardées localement. Connectez-vous pour la synchronisation.',
        color: 'yellow',
        autoClose: 3000,
      });
    } finally {
      setIsSaving(false);
    }
  }, [getUserSpecificKey]);

  // Handle note changes with auto-save
  const handleNotesChange = useCallback((value: string) => {
    setNotes(value);
    setHasUnsavedChanges(true);

    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Set new auto-save timeout (2 seconds)
    autoSaveTimeoutRef.current = setTimeout(async () => {
      await saveNotes(value);
    }, 2000);
  }, [saveNotes]);

  // Handle content change from contentEditable
  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      handleNotesChange(content);
    }
  }, [handleNotesChange]);

  // Handle font size change
  const handleFontSizeChange = (newSize: string | null) => {
    if (newSize) {
      setFontSize(newSize);
      localStorage.setItem('tcf-notebook-fontsize', newSize);
    }
  };

  // Handle color change
  const handleColorChange = (color: string) => {
    setSelectedColor(color);
    localStorage.setItem('tcf-notebook-color', color);
  };

  // Execute rich text commands with toggle support
  const executeCommand = useCallback((command: string, value?: string) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
      handleContentChange();
    }
  }, [handleContentChange]);

  // Check if a formatting is currently active
  const isFormatActive = useCallback((command: string) => {
    try {
      return document.queryCommandState(command);
    } catch (error) {
      return false;
    }
  }, []);

  // Check if text has highlight background
  const isHighlightActive = useCallback(() => {
    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return false;
      
      const range = selection.getRangeAt(0);
      let element = range.startContainer.nodeType === Node.TEXT_NODE 
        ? range.startContainer.parentElement 
        : range.startContainer as Element;
      
      // Check if current element or any parent has highlight background
      while (element && element !== editorRef.current) {
        const bgColor = window.getComputedStyle(element).backgroundColor;
        const style = (element as HTMLElement).style?.backgroundColor;
        
        // Check for yellow highlight colors (various formats) - both light and dark mode
        if (
          bgColor === 'rgb(255, 235, 59)' ||
          bgColor === 'rgb(255, 212, 59)' || // Dark mode highlight color
          bgColor === 'yellow' ||
          style === 'rgb(255, 235, 59)' ||
          style === 'rgb(255, 212, 59)' || // Dark mode highlight color
          style === 'yellow' ||
          style === '#ffeb3b' ||
          style === '#ffd43b' // Dark mode highlight color
        ) {
          return true;
        }
        element = element.parentElement;
      }
      return false;
    } catch (error) {
      return false;
    }
  }, []);

  // Update formatting states for button visual feedback
  const updateFormattingStates = useCallback(() => {
    setIsBoldActive(isFormatActive('bold'));
    setIsHighlightActiveState(isHighlightActive());
    setIsColorActive(selectedColor !== '#000000');
  }, [isFormatActive, isHighlightActive, selectedColor]);

  // Format functions for rich text with toggle support
  const makeBold = useCallback(() => {
    executeCommand('bold');
    setTimeout(updateFormattingStates, 10); // Small delay to ensure DOM is updated
  }, [executeCommand, updateFormattingStates]);
  
  const makeHighlight = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      editorRef.current?.focus();
      return;
    }

    if (isHighlightActive()) {
      // Remove highlight - use removeFormat or set background to transparent
      executeCommand('hiliteColor', 'transparent');
    } else {
      // Add highlight with theme-aware color
      const highlightColor = resolvedTheme === 'dark' ? '#ffd43b' : '#ffeb3b';
      executeCommand('hiliteColor', highlightColor);
    }
    setTimeout(updateFormattingStates, 10); // Small delay to ensure DOM is updated
  }, [executeCommand, isHighlightActive, updateFormattingStates]);
  
  const applyColor = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      editorRef.current?.focus();
      return;
    }

    // Check if the selected text already has the same color
    try {
      const range = selection.getRangeAt(0);
      let element = range.startContainer.nodeType === Node.TEXT_NODE 
        ? range.startContainer.parentElement 
        : range.startContainer as Element;
      
      let hasCurrentColor = false;
      while (element && element !== editorRef.current) {
        const color = window.getComputedStyle(element).color;
        const style = (element as HTMLElement).style?.color;
        
        // Convert selectedColor to rgb format for comparison
        const tempDiv = document.createElement('div');
        tempDiv.style.color = selectedColor;
        const rgbColor = window.getComputedStyle(tempDiv).color;
        
        if (color === rgbColor || style === selectedColor || style === rgbColor) {
          hasCurrentColor = true;
          break;
        }
        element = element.parentElement;
      }
      
      if (hasCurrentColor) {
        // Remove color by setting to default black
        executeCommand('foreColor', '#000000');
        setSelectedColor('#000000');
      } else {
        // Apply the selected color
        executeCommand('foreColor', selectedColor);
      }
    } catch (error) {
      // Fallback: just apply the color
      executeCommand('foreColor', selectedColor);
    }
    setTimeout(updateFormattingStates, 10); // Small delay to ensure DOM is updated
  }, [executeCommand, selectedColor, updateFormattingStates]);

  // Update formatting states on selection change
  useEffect(() => {
    const handleSelectionChange = () => {
      updateFormattingStates();
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, [updateFormattingStates]);

  // Insert bullet point with Google Docs-style indentation and different bullet types
  const insertBulletPoint = useCallback(() => {
    if (editorRef.current) {
      editorRef.current.focus();
      
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;
      
      const range = selection.getRangeAt(0);
      let currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
        ? range.startContainer.parentElement 
        : range.startContainer as Element;
      
      // Find the current list item and list
      let currentListItem: Element | null = null;
      let currentList: Element | null = null;
      let currentIndentLevel = 0;
      
      // Traverse up to find current list context
      let element = currentElement;
      while (element && element !== editorRef.current) {
        if (element.tagName === 'LI') {
          currentListItem = element;
        }
        if (element.tagName === 'UL' || element.tagName === 'OL') {
          currentList = element;
          // Count indent level by counting nested lists
          let parent = element.parentElement;
          while (parent && parent !== editorRef.current) {
            if (parent.tagName === 'LI') {
              currentIndentLevel++;
              break;
            }
            parent = parent.parentElement;
          }
          break;
        }
        element = element.parentElement;
      }
      
      if (currentListItem && currentList) {
        // We're already in a list - create indented sub-bullet
        const newIndentLevel = currentIndentLevel + 1;
        
        // Create new nested list with appropriate style
        const nestedList = document.createElement('ul');
        nestedList.style.margin = '2px 0';
        nestedList.style.paddingLeft = '20px';
        
        // Set bullet style based on indent level
        const bulletStyles = ['disc', 'circle', 'square'];
        nestedList.style.listStyleType = bulletStyles[newIndentLevel % bulletStyles.length];
        
        // Create new list item
        const newListItem = document.createElement('li');
        newListItem.innerHTML = '<br>';
        nestedList.appendChild(newListItem);
        
        // Insert the nested list at the end of current list item
        currentListItem.appendChild(nestedList);
        
        // Move cursor to the new list item
        const newRange = document.createRange();
        newRange.setStart(newListItem, 0);
        newRange.setEnd(newListItem, 0);
        selection.removeAllRanges();
        selection.addRange(newRange);
        
      } else if (currentList && !currentListItem) {
        // We're in a list but not in a list item - add new item to current list
        const newListItem = document.createElement('li');
        newListItem.innerHTML = '<br>';
        currentList.appendChild(newListItem);
        
        // Move cursor to the new list item
        const newRange = document.createRange();
        newRange.setStart(newListItem, 0);
        newRange.setEnd(newListItem, 0);
        selection.removeAllRanges();
        selection.addRange(newRange);
        
      } else {
        // Not in a list - create new top-level list
        const newList = document.createElement('ul');
        newList.style.margin = '4px 0';
        newList.style.paddingLeft = '20px';
        newList.style.listStyleType = 'disc'; // Always start with disc for top level
        
        const newListItem = document.createElement('li');
        newListItem.innerHTML = '<br>';
        newList.appendChild(newListItem);
        
        // Insert the list at cursor position
        try {
          range.deleteContents();
          range.insertNode(newList);
        } catch (error) {
          // Fallback: append to the end
          editorRef.current.appendChild(newList);
        }
        
        // Move cursor to the new list item
        const newRange = document.createRange();
        newRange.setStart(newListItem, 0);
        newRange.setEnd(newListItem, 0);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
      
      handleContentChange();
    }
  }, [handleContentChange]);

  // Outdent a list item (reduce indent level)
  const outdentListItem = useCallback((listItem: Element) => {
    const parentList = listItem.parentElement;
    if (!parentList || (parentList.tagName !== 'UL' && parentList.tagName !== 'OL')) {
      return; // Not in a list
    }
    
    // Find the parent list item (grandparent)
    const grandParentListItem = parentList.parentElement;
    if (!grandParentListItem || grandParentListItem.tagName !== 'LI') {
      // We're at the top level - can't outdent further
      return;
    }
    
    // Find the great-grandparent list
    const greatGrandParentList = grandParentListItem.parentElement;
    if (!greatGrandParentList || (greatGrandParentList.tagName !== 'UL' && greatGrandParentList.tagName !== 'OL')) {
      return;
    }
    
    // Move the list item to the parent level
    parentList.removeChild(listItem);
    
    // Insert the list item after the grandparent list item in the great-grandparent list
    const nextSibling = grandParentListItem.nextSibling;
    if (nextSibling) {
      greatGrandParentList.insertBefore(listItem, nextSibling);
    } else {
      greatGrandParentList.appendChild(listItem);
    }
    
    // Clean up empty lists
    if (parentList.children.length === 0) {
      grandParentListItem.removeChild(parentList);
    }
    
    // Maintain cursor position
    const selection = window.getSelection();
    if (selection) {
      const range = document.createRange();
      range.setStart(listItem, 0);
      range.setEnd(listItem, 0);
      selection.removeAllRanges();
      selection.addRange(range);
    }
    
    handleContentChange();
  }, [handleContentChange]);

  // Manual save
  const handleManualSave = async () => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    await saveNotes(notes);
    notifications.show({
      title: 'Notes sauvegardées',
      message: 'Vos notes ont été sauvegardées avec succès',
      color: 'green',
      autoClose: 2000,
    });
  };



  // Get text content length from HTML (for character count)
  const getTextLength = useCallback(() => {
    if (editorRef.current) {
      const textContent = editorRef.current.textContent || '';
      return textContent.length;
    }
    return notes.length;
  }, [notes]);

  // Check if editor has content
  const hasContent = useCallback(() => {
    if (editorRef.current) {
      const textContent = editorRef.current.textContent || '';
      return textContent.trim().length > 0;
    }
    return notes.trim().length > 0;
  }, [notes]);

  // Clear all notes - now clears from database
  const handleClearNotes = async () => {
    try {
      // Delete from database
      await api.delete('/user/notebook');
      
      // Clear local state
      setNotes('');
      if (editorRef.current) {
      }
      setHasUnsavedChanges(false);
      setLastSaved(null);
      
      // Also clear localStorage backup
      const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
      const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
      localStorage.removeItem(userSpecificNotesKey);
      localStorage.removeItem(userSpecificTimestampKey);
      
      closeDeleteModal();
      
      notifications.show({
        title: 'Notes supprimées',
        message: 'Toutes vos notes ont été supprimées',
        color: 'blue',
        autoClose: 2000,
      });
    } catch (error) {
      console.error('Failed to delete notes from database:', error);
      
      // Fallback to localStorage clear
      setNotes('');
      if (editorRef.current) {
      }
      setHasUnsavedChanges(false);
      localStorage.removeItem(getUserSpecificKey('tcf-universal-notebook'));
      localStorage.removeItem(getUserSpecificKey('tcf-universal-notebook-timestamp'));
      setLastSaved(null);
      closeDeleteModal();
      
      notifications.show({
        title: 'Notes supprimées localement',
        message: 'Notes supprimées de votre appareil. Connectez-vous pour la synchronisation.',
        color: 'yellow',
        autoClose: 3000,
      });
    }
  };

  // Save notebook with custom name
  const handleSaveNotebook = async () => {
    if (!notebookName.trim()) {
      notifications.show({
        title: t('notebook.error', 'Error'),
        message: t('notebook.nameRequired', 'Please enter a notebook name'),
        color: 'red',
        autoClose: 3000,
      });
      return;
    }

    try {
      setIsSavingNotebook(true);

      const result = await notebookApi.saveGlobalNotebookAs(notebookName.trim(), true);

      // Clear the global notebook locally since it was cleared on the server
      setNotes('');
      if (editorRef.current) {
        setTimeout(() => {
          if (editorRef.current) {
            editorRef.current.innerHTML = '';

          }
        }, 100);
      }
      setHasUnsavedChanges(false);
      setLastSaved(null);

      // Clear localStorage backup
      const userSpecificNotesKey = getUserSpecificKey('tcf-universal-notebook');
      const userSpecificTimestampKey = getUserSpecificKey('tcf-universal-notebook-timestamp');
      localStorage.removeItem(userSpecificNotesKey);
      localStorage.removeItem(userSpecificTimestampKey);

      closeSaveModal();
      setNotebookName('');

      notifications.show({
        title: t('notebook.saveSuccess', 'Notebook Saved'),
        message: t('notebook.saveSuccessMessage', `Notebook "${result.notebook_name}" has been saved successfully`),
        color: 'green',
        autoClose: 4000,
      });

    } catch (error: any) {
      console.error('Failed to save notebook:', error);

      let errorMessage = t('notebook.saveErrorMessage', 'Failed to save notebook');

      if (error.response?.status === 409) {
        errorMessage = t('notebook.nameExists', 'A notebook with this name already exists');
      } else if (error.response?.status === 404) {
        errorMessage = t('notebook.noContent', 'No content to save');
      } else if (error.response?.status === 400) {
        errorMessage = t('notebook.emptyContent', 'Cannot save empty notebook');
      }

      notifications.show({
        title: t('notebook.saveError', 'Save Failed'),
        message: errorMessage,
        color: 'red',
        autoClose: 5000,
      });
    } finally {
      setIsSavingNotebook(false);
    }
  };

  // Resize functionality
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const sidebar = sidebarRef.current;
      if (!sidebar) return;
      
      const rect = sidebar.getBoundingClientRect();
      const newWidth = e.clientX - rect.left;
      
      // Constrain width between 150px and 400px (larger range)
      const constrainedWidth = Math.max(250, Math.min(400, newWidth));
      setSidebarWidth(constrainedWidth);
      localStorage.setItem('tcf-notebook-width', constrainedWidth.toString());
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'ew-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const handleResizeStart = () => {
    setIsResizing(true);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // Format last saved time
  const getLastSavedText = () => {
    if (!lastSaved) return null;
    
    const now = new Date();
    const diffMs = now.getTime() - lastSaved.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    
    if (diffMinutes < 1) return 'À l\'instant';
    if (diffMinutes === 1) return 'Il y a 1 minute';
    if (diffMinutes < 60) return `Il y a ${diffMinutes} minutes`;
    
    return lastSaved.toLocaleTimeString(undefined, { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const openDeleteModal = () => setDeleteModalOpened(true);
  const closeDeleteModal = () => setDeleteModalOpened(false);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'b':
          e.preventDefault();
          makeBold();
          break;
        case 'u':
          e.preventDefault();
          makeHighlight();
          break;
        case 's':
          e.preventDefault();
          handleManualSave();
          break;
        default:
          break;
      }
    } else {
      // Handle Tab and Shift+Tab for list indentation
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
          ? range.startContainer.parentElement 
          : range.startContainer as Element;
        
        // Check if we're in a list item
        let currentListItem: Element | null = null;
        let element = currentElement;
        while (element && element !== editorRef.current) {
          if (element.tagName === 'LI') {
            currentListItem = element;
            break;
          }
          element = element.parentElement;
        }
        
        if (currentListItem) {
          if (e.key === 'Tab') {
            e.preventDefault();
            if (e.shiftKey) {
              // Shift+Tab: Outdent (decrease indent level)
              outdentListItem(currentListItem);
            } else {
              // Tab: Indent (increase indent level)
              insertBulletPoint();
            }
          }
        }
      }
      
      // Create bullet list on Enter after an empty line
      if (e.key === 'Enter') {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
            ? range.startContainer.parentElement 
            : range.startContainer as Element;
          
          // Check if current line is empty and not in a list
          let inList = false;
          let element = currentElement;
          while (element && element !== editorRef.current) {
            if (element.tagName === 'UL' || element.tagName === 'OL' || element.tagName === 'LI') {
              inList = true;
              break;
            }
            element = element.parentElement;
          }
          
          // If we're at the end of an empty list item, exit the list
          if (inList && currentElement && currentElement.tagName === 'LI') {
            const listItem = currentElement as HTMLLIElement;
            const textContent = listItem.textContent?.trim() || '';
            if (textContent === '' || textContent === '\n') {
              e.preventDefault();
              // Remove empty list item and exit list
              const parentList = listItem.parentElement;
              if (parentList) {
                listItem.remove();
                // Create a new paragraph after the list
                const newP = document.createElement('p');
                newP.innerHTML = '<br>';
                parentList.parentNode?.insertBefore(newP, parentList.nextSibling);
                
                // Move cursor to new paragraph
                const newRange = document.createRange();
                newRange.setStart(newP, 0);
                newRange.setEnd(newP, 0);
                selection.removeAllRanges();
                selection.addRange(newRange);
                
                handleContentChange();
              }
            }
          }
        }
      }
    }
  }, [makeBold, makeHighlight, handleManualSave, insertBulletPoint, handleContentChange, outdentListItem]);

  if (!isExpanded) {
    return null; // Don't render anything when collapsed
  }

  return (
    <>
      <Box
        ref={sidebarRef}
        className={className}
        style={{
          width: `${sidebarWidth}px`,
          height: 'calc(80vh - 200px)',
          minHeight: 'calc(80vh - 200px)',
          maxHeight: 'calc(80vh - 200px)',
          backgroundColor: themeColors.surface,
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          top: '50%',
          transform: 'translateY(-50%)',
          boxShadow: resolvedTheme === 'dark'
            ? '-2px 0 8px rgba(0,0,0,0.3)'
            : '-2px 0 8px rgba(0,0,0,0.08)',
          borderLeft: `1px solid ${themeColors.border}`,
          borderRadius: '12px 0 0 12px',
          overflow: 'hidden',
          transition: 'background-color 0.3s ease, border-color 0.3s ease'
        }}
      >
        {/* Top Corner Resize Handle */}
        <Box
          ref={resizeRef}
          onMouseDown={handleResizeStart}
          style={{
            position: 'absolute',
            right: '-6px',
            top: '-3px',
            width: '12px',
            height: '12px',
            cursor: 'ne-resize',
            zIndex: 5,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'transparent',
            borderRadius: '2px'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = resolvedTheme === 'dark'
              ? 'rgba(30, 144, 255, 0.3)'
              : 'rgba(34, 139, 230, 0.2)';
          }}
          onMouseLeave={(e) => {
            if (!isResizing) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          <Box
            style={{
              width: '8px',
              height: '8px',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gridTemplateRows: '1fr 1fr',
              gap: '1px'
            }}
          >
            {[...Array(4)].map((_, i) => (
              <Box
                key={i}
                style={{
                  width: '2px',
                  height: '2px',
                  backgroundColor: themeColors.primary,
                  borderRadius: '50%',
                  border: `0.5px solid ${themeColors.surface}`,
                  boxShadow: resolvedTheme === 'dark'
                    ? '0 0.5px 1px rgba(0,0,0,0.4)'
                    : '0 0.5px 1px rgba(0,0,0,0.2)'
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Bottom Corner Resize Handle */}
        <Box
          onMouseDown={handleResizeStart}
          style={{
            position: 'absolute',
            right: '-6px',
            bottom: '-3px',
            width: '12px',
            height: '12px',
            cursor: 'se-resize',
            zIndex: 5,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'transparent',
            borderRadius: '2px'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = resolvedTheme === 'dark'
              ? 'rgba(30, 144, 255, 0.3)'
              : 'rgba(34, 139, 230, 0.2)';
          }}
          onMouseLeave={(e) => {
            if (!isResizing) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          <Box
            style={{
              width: '8px',
              height: '8px',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gridTemplateRows: '1fr 1fr',
              gap: '1px'
            }}
          >
            {[...Array(4)].map((_, i) => (
              <Box
                key={i}
                style={{
                  width: '2px',
                  height: '2px',
                  backgroundColor: themeColors.primary,
                  borderRadius: '50%',
                  border: `0.5px solid ${themeColors.surface}`,
                  boxShadow: resolvedTheme === 'dark'
                    ? '0 0.5px 1px rgba(0,0,0,0.4)'
                    : '0 0.5px 1px rgba(0,0,0,0.2)'
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Minimal Header */}
        <Box px="sm" py="xs" style={{ backgroundColor: themeColors.surfaceHover, borderBottom: `1px solid ${themeColors.border}`, flexShrink: 0 }}>
          <Group justify="space-between" align="center">
            <Text fw={600} size="xs" c={themeColors.textPrimary}>Carnet</Text>
            <Group gap="xs">
              <Tooltip label={t('notebook.saveAs', 'Save as...')}>
                <ActionIcon
                  variant="subtle"
                  color="blue"
                  size="xs"
                  onClick={openSaveModal}
                  disabled={!hasContent() || isLoading || isSaving}
                >
                  <IconBookmark size={10} />
                </ActionIcon>
              </Tooltip>
              <ActionIcon
                variant="subtle"
                color="red"
                size="xs"
                onClick={openDeleteModal}
                disabled={!hasContent() || isLoading || isSaving}
              >
                <IconTrash size={10} />
              </ActionIcon>
            </Group>
          </Group>
        </Box>

        {/* Formatting Toolbar */}
        <Box px="xs" py="xs" style={{ backgroundColor: themeColors.surface, borderBottom: `1px solid ${themeColors.border}`, flexShrink: 0 }}>
          <Group gap="xs" justify="space-between">
            <Group gap="xs">
              <Tooltip label="Bold">
                <ActionIcon
                  variant="subtle"
                  size="xs"
                  onClick={makeBold}
                  styles={{
                    root: {
                      backgroundColor: isBoldActive ? themeColors.primary : 'transparent',
                      color: isBoldActive ? 'white' : themeColors.textPrimary,
                      '&:hover': {
                        backgroundColor: isBoldActive ? themeColors.primaryHover : themeColors.surfaceHover,
                      },
                    },
                  }}
                >
                  <IconBold size={12} />
                </ActionIcon>
              </Tooltip>
              
              <Tooltip label="Highlight">
                <ActionIcon
                  variant="subtle"
                  size="xs"
                  onClick={makeHighlight}
                  styles={{
                    root: {
                      backgroundColor: isHighlightActiveState
                        ? (resolvedTheme === 'dark' ? '#b8860b' : '#ffd43b')
                        : 'transparent',
                      color: isHighlightActiveState
                        ? (resolvedTheme === 'dark' ? 'white' : 'black')
                        : themeColors.textPrimary,
                      '&:hover': {
                        backgroundColor: isHighlightActiveState
                          ? (resolvedTheme === 'dark' ? '#9a7209' : '#fcc419')
                          : themeColors.surfaceHover,
                      },
                    },
                  }}
                >
                  <IconHighlight size={12} />
                </ActionIcon>
              </Tooltip>
              
              <Popover width={200} position="bottom" withArrow>
                <Popover.Target>
                  <Tooltip label="Text color">
                    <ActionIcon
                      variant="subtle"
                      size="xs"
                      styles={{
                        root: {
                          backgroundColor: isColorActive ? themeColors.primary : 'transparent',
                          color: isColorActive ? 'white' : themeColors.textPrimary,
                          '&:hover': {
                            backgroundColor: isColorActive ? themeColors.primaryHover : themeColors.surfaceHover,
                          },
                        },
                      }}
                    >
                      <IconPalette size={12} />
                    </ActionIcon>
                  </Tooltip>
                </Popover.Target>
                <Popover.Dropdown style={{ backgroundColor: themeColors.surface, borderColor: themeColors.border }}>
                  <Stack gap="xs">
                    <Text size="xs" fw={500} c={themeColors.textPrimary}>Select color:</Text>
                    <Group gap="xs">
                      {['#000000', '#e03131', '#1c7ed6', '#37b24d', '#f59f00', '#9c36b5'].map((color) => (
                        <Box
                          key={color}
                          onClick={() => {
                            handleColorChange(color);
                            applyColor();
                          }}
                          style={{
                            width: '20px',
                            height: '20px',
                            backgroundColor: color,
                            borderRadius: '50%',
                            cursor: 'pointer',
                            border: selectedColor === color ? `2px solid ${themeColors.primary}` : `1px solid ${themeColors.border}`
                          }}
                        />
                      ))}
                    </Group>
                  </Stack>
                </Popover.Dropdown>
              </Popover>
              
              <Tooltip label="Bullet point (click again to indent)">
                <ActionIcon
                  variant="subtle"
                  size="xs"
                  onClick={insertBulletPoint}
                  styles={{
                    root: {
                      color: themeColors.textPrimary,
                      '&:hover': {
                        backgroundColor: themeColors.surfaceHover,
                      },
                    },
                  }}
                >
                  <IconList size={12} />
                </ActionIcon>
              </Tooltip>
            </Group>
            
            <Group gap="2px">
              <ActionIcon
                variant="subtle"
                size="xs"
                onClick={() => {
                  const newSize = Math.max(10, parseInt(fontSize) - 2).toString();
                  handleFontSizeChange(newSize);
                }}
                disabled={parseInt(fontSize) <= 10}
                styles={{
                  root: {
                    color: themeColors.textPrimary,
                    '&:hover': {
                      backgroundColor: themeColors.surfaceHover,
                    },
                  },
                }}
              >
                <Text size="xs" fw={600} c={themeColors.textPrimary}>−</Text>
              </ActionIcon>
              
              <Box
                style={{
                  minWidth: '28px',
                  height: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: themeColors.surfaceHover,
                  border: `1px solid ${themeColors.border}`,
                  borderRadius: '4px',
                  fontSize: '11px',
                  fontWeight: 500,
                  color: themeColors.textPrimary
                }}
              >
                {fontSize}
              </Box>
              
              <ActionIcon
                variant="subtle"
                size="xs"
                onClick={() => {
                  const newSize = Math.min(24, parseInt(fontSize) + 2).toString();
                  handleFontSizeChange(newSize);
                }}
                disabled={parseInt(fontSize) >= 24}
                styles={{
                  root: {
                    color: themeColors.textPrimary,
                    '&:hover': {
                      backgroundColor: themeColors.surfaceHover,
                    },
                  },
                }}
              >
                <Text size="xs" fw={600} c={themeColors.textPrimary}>+</Text>
              </ActionIcon>
            </Group>
          </Group>
        </Box>

        {/* Notes Editor - Takes up most of the space */}
        <Box style={{ flex: 1, display: 'flex', flexDirection: 'column', padding: '4px', overflow: 'hidden', minHeight: 0 }}>
          {isLoading ? (
            <Box style={{ 
              flex: 1, 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              minHeight: '200px'
            }}>
              <Stack align="center" gap="xs">
                <IconNotebook size={24} color={themeColors.textSecondary} />
                <Text size="sm" c={themeColors.textSecondary}>{t('loadingStates.data')}</Text>
              </Stack>
            </Box>
          ) : (
            <div
              ref={editorRef}
              contentEditable={true}
              className="rich-text-editor"
              style={{ 
                flex: 1,
                minHeight: 0,
                fontSize: `${fontSize}px`,
                lineHeight: 1.5,
                border: 'none',
                outline: 'none',
                padding: '8px',
                overflow: 'auto',
                backgroundColor: themeColors.surface,
                color: themeColors.textPrimary,
                fontFamily: 'inherit'
              }}
              onInput={handleContentChange}
              onKeyDown={handleKeyDown}
              onPaste={(e) => {
                // Handle paste to maintain formatting
                e.preventDefault();
                const text = e.clipboardData?.getData('text/plain') || '';
                document.execCommand('insertText', false, text);
                handleContentChange();
              }}
              suppressContentEditableWarning={true}
              data-placeholder={notes ? '' : t('notebook.placeholder')}
            />
          )}
        </Box>

        {/* Add CSS styling for rich text editor */}
        <style dangerouslySetInnerHTML={{
          __html: `
            .rich-text-editor[data-placeholder]:empty::before {
              content: attr(data-placeholder);
              color: ${themeColors.textSecondary};
              cursor: text;
            }
            
            .rich-text-editor ul {
              margin: 4px 0;
              padding-left: 20px;
            }
            
            .rich-text-editor li {
              margin: 2px 0;
              list-style-position: outside;
            }
            
            /* Level 1: Disc bullets */
            .rich-text-editor ul {
              list-style-type: disc;
            }
            
            /* Level 2: Circle bullets */
            .rich-text-editor ul ul {
              list-style-type: circle;
              margin: 2px 0;
              padding-left: 18px;
            }
            
            /* Level 3: Square bullets */
            .rich-text-editor ul ul ul {
              list-style-type: square;
              margin: 2px 0;
              padding-left: 16px;
            }
            
            /* Level 4+: Cycle back to disc */
            .rich-text-editor ul ul ul ul {
              list-style-type: disc;
            }
            
            /* Level 5+: Circle again */
            .rich-text-editor ul ul ul ul ul {
              list-style-type: circle;
            }
            
            /* Level 6+: Square again */
            .rich-text-editor ul ul ul ul ul ul {
              list-style-type: square;
            }
            
            .rich-text-editor strong {
              font-weight: bold;
            }
            
            .rich-text-editor em {
              font-style: italic;
            }
            
            .rich-text-editor p {
              margin: 4px 0;
            }
            
            .rich-text-editor [style*="background-color"] {
              padding: 1px 2px;
              border-radius: 2px;
            }
          `
        }} />

        {/* Minimal Footer */}
        <Box px="sm" py="xs" style={{
          borderTop: `1px solid ${themeColors.border}`,
          backgroundColor: themeColors.background,
          flexShrink: 0,
          transition: 'background-color 0.3s ease, border-color 0.3s ease'
        }}>
          <Group justify="space-between" align="center">
            <Group gap="xs">
              {isSaving && (
                <Badge size="xs" color="blue" variant="dot" />
              )}
              {hasUnsavedChanges && !isSaving && (
                <Badge size="xs" color="orange" variant="dot" />
              )}
              {lastSaved && !hasUnsavedChanges && !isSaving && (
                <Badge size="xs" color="green" variant="dot" />
              )}
              <Text size="xs" c={themeColors.textSecondary}>
                {isLoading ? t('loadingStates.data') : getTextLength()}
              </Text>
            </Group>
            <ActionIcon
              variant="subtle"
              size="xs"
              onClick={handleManualSave}
              disabled={!hasUnsavedChanges || isSaving || isLoading}
              loading={isSaving}
              styles={{
                root: {
                  color: themeColors.primary,
                  '&:hover': {
                    backgroundColor: themeColors.surfaceHover,
                  },
                },
              }}
            >
              <IconDeviceFloppy size={10} />
            </ActionIcon>
          </Group>
        </Box>
      </Box>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={deleteModalOpened}
        onClose={closeDeleteModal}
        title={t('notebook.deleteModal.title')}
        centered
        size="sm"
        styles={{
          content: {
            backgroundColor: themeColors.surface,
          },
          header: {
            backgroundColor: themeColors.surface,
            borderBottom: `1px solid ${themeColors.border}`,
          },
          title: {
            color: themeColors.textPrimary,
          },
        }}
      >
        <Stack gap="md">
          <Text c={themeColors.textPrimary}>
            {t('notebook.deleteModal.message')}
          </Text>
          
          <Group justify="flex-end" gap="sm">
            <Button
              variant="outline"
              onClick={closeDeleteModal}
              styles={{
                root: {
                  borderColor: themeColors.border,
                  color: themeColors.textPrimary,
                  '&:hover': {
                    backgroundColor: themeColors.surfaceHover,
                  },
                },
              }}
            >
              {t('notebook.deleteModal.cancel')}
            </Button>
            <Button
              color="red"
              onClick={handleClearNotes}
              leftSection={<IconTrash size={14} />}
              styles={{
                root: {
                  backgroundColor: themeColors.speaking,
                  '&:hover': {
                    backgroundColor: resolvedTheme === 'dark' ? '#b91c1c' : '#dc2626',
                  },
                },
              }}
            >
              {t('notebook.deleteModal.confirm')}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Save Notebook Modal */}
      <Modal
        opened={saveModalOpened}
        onClose={closeSaveModal}
        title={t('notebook.saveAs', 'Save Notebook As...')}
        centered
        size="sm"
        styles={{
          content: {
            backgroundColor: themeColors.surface,
          },
          header: {
            backgroundColor: themeColors.surface,
            borderBottom: `1px solid ${themeColors.border}`,
          },
          title: {
            color: themeColors.textPrimary,
          },
        }}
      >
        <Stack gap="md">
          <Text c={themeColors.textSecondary} size="sm">
            {t('notebook.saveAsDescription', 'Give your notebook a name to save it. Your current global notebook will be cleared.')}
          </Text>

          <TextInput
            label={t('notebook.notebookName', 'Notebook Name')}
            placeholder={t('notebook.namePlaceholder', 'Enter notebook name...')}
            value={notebookName}
            onChange={(event) => setNotebookName(event.currentTarget.value)}
            disabled={isSavingNotebook}
            styles={{
              label: {
                color: themeColors.textPrimary,
              },
              input: {
                backgroundColor: themeColors.surface,
                borderColor: themeColors.border,
                color: themeColors.textPrimary,
                '&:focus': {
                  borderColor: themeColors.primary,
                },
              },
            }}
            onKeyDown={(event) => {
              if (event.key === 'Enter' && !isSavingNotebook) {
                handleSaveNotebook();
              }
            }}
          />

          <Group justify="flex-end" gap="sm">
            <Button
              variant="outline"
              onClick={closeSaveModal}
              disabled={isSavingNotebook}
              styles={{
                root: {
                  borderColor: themeColors.border,
                  color: themeColors.textPrimary,
                  '&:hover': {
                    backgroundColor: themeColors.surfaceHover,
                  },
                },
              }}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              onClick={handleSaveNotebook}
              loading={isSavingNotebook}
              leftSection={<IconBookmark size={14} />}
              styles={{
                root: {
                  backgroundColor: themeColors.primary,
                  '&:hover': {
                    backgroundColor: themeColors.primaryHover,
                  },
                },
              }}
            >
              {t('notebook.save', 'Save')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
};