{"home": {"title": "Welcome to Chez-TCFCA", "subtitle": "Your comprehensive platform for TCF Canada test preparation with interactive exercises and expert guidance.", "loading": "Loading tests...", "errorTitle": "Loading error", "errorMessage": "Unable to load tests. Please try again later.", "membershipWarningTitle": "Membership required", "membershipWarningText": "You must have an active membership to access all tests.", "membershipWarningLink": "See membership options", "startButton": "Start", "comingSoon": "Coming soon", "ctaTitle": "Start your preparation now", "ctaSubtitle": "Create an account to access free tests and track your progress.", "registerButton": "Register for free", "loginButton": "Log in", "hero": {"title": "Chez-TCFCA"}, "whyChooseUs": {"title": "Why Choose Our Platform?", "subtitle": "Six Core Advantages", "description": "Six core advantages to help you prepare efficiently for TCF Canada", "subdescription": "Comprehensive features designed for optimal learning outcomes"}, "testSections": {"title": "开始您的备考之旅", "subtitle": "Start Your Practice Journey", "description": "选择您想要练习的测试模块，开始高效备考"}, "welcome": {"title": "Welcome to Your TCF Canada Success Journey", "subtitle": "Master the TCF Canada exam with our comprehensive, interactive platform designed for optimal learning and success.", "description": "TCF Canada provides the most effective preparation tools with real exam questions, detailed explanations, and personalized progress tracking. Join thousands of successful candidates who achieved their immigration goals with our platform."}, "features": {"title": "Why <PERSON><PERSON>ez-TCFCA?", "subtitle": "Discover the powerful features that make our platform the #1 choice for TCF Canada preparation", "comprehensive": {"title": "Deduplicated & Classified Question Bank", "description": "Access thousands of authentic TCF Canada questions with advanced deduplication and classification system for efficient, targeted learning of high-value content."}, "enhanced": {"title": "Enhanced Audio Quality", "description": "All low-quality listening audio has been enhanced to ensure optimal practice experience."}, "verified": {"title": "Verified Answers", "description": "All answers have been thoroughly reviewed and verified for accuracy and reliability."}, "explanations": {"title": "Detailed Explanations", "description": "Every question comes with detailed explanations to help you understand concepts and strategies."}, "customizable": {"title": "Intensive Listening", "description": "Support for intensive listening practice. All listening questions have been segmented by sentence - click on any sentence to play that specific part."}, "mockExams": {"title": "<PERSON><PERSON>", "description": "Practice with realistic timed mock exams to build confidence and time management skills."}, "interactive": {"title": "Interactive Learning Tools", "description": "Highlight text, take notes, bookmark questions, and track your progress with our advanced learning features."}, "expert": {"title": "Expert-Verified Content", "description": "All questions and answers are verified by language experts, with controversial questions clearly flagged for transparency."}, "personalized": {"title": "Personalized Study Experience", "description": "Adaptive learning paths, detailed analytics, and customized practice sessions based on your performance and goals."}, "realistic": {"title": "Realistic <PERSON><PERSON>", "description": "Practice with timed mock exams that simulate real test conditions, helping you build confidence and time management skills."}, "analytics": {"title": "Detailed Progress Analytics", "description": "Track your performance with comprehensive statistics, identify weak areas, and monitor your improvement over time."}}, "guide": {"title": "About TCF Canada", "description1": "This platform is specifically designed for TCF Canada preparation. With our comprehensive question database, you'll encounter most questions that appear in the actual exam, making this the most effective way to prepare if you're committed to completing all available tests.", "description2": "Our platform provides comprehensive tools and resources to support your exam preparation journey.", "contactNote": "We welcome you to use \"Contact Support\" to share your feedback or report any issues."}, "announcements": {"title": "Latest Updates & Opportunities", "description": "Stay informed about our latest features, offers, and ways to contribute", "promoCode": {"title": "Limited Time Offer", "message": "Get 3 days of premium access with promo code <strong>TCFCA</strong>! Perfect for trying out all our advanced features before committing to a full membership.<br/><br/><strong>Important:</strong> Only <strong>30 codes available daily</strong>, refreshed every day at midnight. Each account can use the promo code <strong>only once</strong>. Don't miss out—claim yours today!", "actionText": "Use Promo Code"}, "bugReport": {"title": "Help Us Improve", "message": "<strong>1.</strong> For the <strong>first bug/error you discover and report</strong> that gets confirmed, you'll receive a <strong>7-day membership promo code</strong> (shareable with others, usable up to 3 times).<br/><br/><strong>2.</strong> For <strong>each subsequent confirmed bug/error</strong> you report, you'll get <strong>3 additional days</strong> of membership.<br/><br/><strong>Exception:</strong> For listening and reading text hallucination errors (such as \"merci\" at the end or repeated sentences), you'll receive <strong>1 day</strong> of membership time.", "actionText": "Report Bug"}, "downloads": {"title": "Download Free Study Materials", "message": "Access our comprehensive collection of free TCF Canada preparation materials, including practice tests, study guides, and reference documents.<br/><br/><strong>Featured Content:</strong> Complete <strong>writing materials with detailed correction examples</strong> and comprehensive <strong>speaking materials with expert correction samples</strong>. Perfect for understanding proper techniques and common mistakes to avoid.", "actionText": "Browse Free Downloads", "loginText": "Login for Free Downloads"}}, "sections": {"title": "Start Your Practice", "listening": {"title": "Listening Comprehension", "description": "Master spoken French with 39 multiple-choice questions, enhanced audio quality, and sentence-by-sentence transcript review."}, "reading": {"title": "Reading Comprehension", "description": "Excel in written French comprehension with highlight tools, note-taking features, and progressive difficulty levels."}, "writing": {"title": "Written Expression", "description": "Perfect your French writing skills with structured tasks, expert corrections, and comprehensive feedback."}, "speaking": {"title": "Oral Expression", "description": "Build confidence in spoken French with realistic scenarios, sample answers, and structured practice sessions."}}}, "navigation": {"title": "Question Navigation", "answered": "Answered", "wrongAnswer": "Wrong answer", "notAnswered": "Not answered", "pointsEach": "pts each", "hint": {"moveMouseToTop": "Move mouse to top to show navigation"}}, "layout": {"logout": "Logout", "login": "<PERSON><PERSON>", "register": "Sign up", "home": "Home", "profile": "Profile", "collection": "My Collection", "notebook": "Notebook", "membership": {"becomeMember": "Become a member", "extendMembership": "Extend membership"}, "support": "Support", "contactSupport": "Contact Support", "quickNavigation": {"title": "Sections", "listening": "Listening", "reading": "Reading", "writing": "Writing", "speaking": "Speaking"}, "tools": {"title": "Tools", "downloadMaterials": "Download Materials"}, "footer": {"copyright": "All rights reserved.", "contactSupport": "Contact Support"}}, "theme": {"switchToDark": "Switch to Dark Mode", "switchToLight": "Switch to Light Mode"}, "common": {"loading": "Loading...", "pleaseWait": "Please wait", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "continue": "Continue", "previous": "Previous", "next": "Next", "start": "Start", "finish": "Finish", "submit": "Submit", "retry": "Retry", "refresh": "Refresh", "search": "Search", "filter": "Filter", "sort": "Sort", "view": "View", "hide": "<PERSON>de", "show": "Show", "expand": "Expand", "collapse": "Collapse", "select": "Select", "unselect": "Unselect", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "free": "Free", "premium": "Premium", "required": "Required", "optional": "Optional", "yes": "Yes", "no": "No", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "available": "Available", "unavailable": "Unavailable", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "public": "Public", "private": "Private", "new": "New", "old": "Old", "updated": "Updated", "created": "Created", "modified": "Modified", "deleted": "Deleted", "and": "and", "highlight": "Highlight", "removeHighlight": "Click to remove this highlight", "clickToRemove": "Click to remove", "autoPlay": "Auto-play", "speed": "Speed", "audioWaitTime": "Wait time before auto-play", "waitTime": {"0": "No delay", "5": "5 seconds", "10": "10 seconds", "15": "15 seconds", "20": "20 seconds", "25": "25 seconds", "30": "30 seconds"}, "bookmarks": {"add": "Add to collection", "remove": "Remove from collection", "added": {"title": "Question saved", "message": "The question has been added to your collection"}, "removed": {"title": "Bookmark removed", "message": "The question has been removed from your collection"}, "errors": {"loginRequired": {"title": "Login required", "message": "You must be logged in to save questions"}, "alreadySaved": {"title": "Already saved", "message": "This question is already in your collection"}, "saveFailed": {"title": "Error", "message": "Unable to save the question. Please try again."}}}}, "loadingStates": {"application": "Loading application...", "initializing": "Initializing...", "tests": "Loading tests...", "testProgress": "Loading test progress...", "speaking": "Loading speaking data...", "questions": "Loading questions...", "data": "Loading data...", "profile": "Loading profile...", "membership": "Loading membership...", "collection": "Loading your collection...", "history": "Loading history...", "exam": "Loading exam...", "mockExam": "Loading mock exam...", "month": "Loading month...", "writing": "Loading writing exercises...", "audio": "Loading audio...", "image": "Loading image...", "saving": "Saving...", "submitting": "Submitting...", "processing": "Processing...", "uploading": "Uploading...", "downloading": "Downloading...", "connecting": "Connecting...", "authenticating": "Authenticating...", "verifying": "Verifying...", "generating": "Generating...", "calculating": "Calculating..."}, "profile": {"title": "My Profile", "accountInfo": "Account Information", "username": "Username", "email": "Email", "joinedDate": "Join Date", "membershipStatus": "Membership Status", "statusLabel": "Status", "type": "Type", "expiresOn": "Expires on", "lifetimeAccess": "Lifetime Subscription - Unlimited Access", "freeAccount": "Free Account", "premiumMember": "Premium Member", "expired": "Expired", "manageSubscription": "Manage Subscription", "subscribe": "Subscribe", "promoCode": "Promo Code", "testHistory": "Test History", "noHistory": "No test history available", "startTesting": "Start a test to see your progress here", "listView": "List view", "gridView": "Grid view", "deleteHistory": "Delete history", "results": "Results", "review": "Review", "notStarted": "Not started", "completed": "Completed", "completedAndGraded": "Completed and graded", "progress": "Progress", "confirmDelete": "Confirm deletion", "deleteHistoryWarning": "Are you sure you want to delete this test history? This action is irreversible.", "currentResults": "Current results", "correct": "correct", "wrong": "wrong", "answers": "answers", "historyDeleted": "History deleted successfully", "deleteError": "Error deleting history", "viewHistoryPage": "View full history page", "quickActions": "Quick Actions", "takeTest": "Take a test", "myNotes": "My notes", "fullHistory": "Full history", "deleteTestHistory": "Delete test history", "deleteWarning": "Are you sure you want to delete the history of this test? This action is irreversible and you will have to restart the test from the beginning.", "testLabel": "Test", "currentScore": "Current score", "cancel": "Cancel", "deleteHistoryButton": "Delete history", "noTestsYet": "No tests taken yet", "takeFirstTest": "Take my first test", "resetFilters": "Reset filters", "noTestsFound": "No tests found with these filters", "sections": {"listening": "Listening Comprehension", "reading": "Reading Comprehension", "writing": "Written Expression", "speaking": "Oral Expression"}, "status": {"lifetime": "Lifetime", "active": "Active", "expired": "Expired", "free": "Free"}, "buttons": {"continue": "Continue", "start": "Start", "results": "Results", "review": "Review"}, "progressText": {"completed": "% completed", "daysLeft": "day", "daysLeftPlural": "days", "hoursLeft": "hour", "hoursLeftPlural": "hours", "minutesLeft": "minute", "minutesLeftPlural": "minutes", "remaining": "remaining"}, "passwordChange": {"title": "Change Password", "button": "Change Password", "emailSent": "Email sent", "linkSent": "A reset link has been sent to", "checkEmail": "Check your email for the reset link", "sendError": "Error sending email", "close": "Close", "securityNote": "For your security, we will send you a reset link by email to change your password.", "emailAccount": "Your account email", "whyMethod": "Why this method?", "securityExplanation": "Sending a verification email ensures that you are the account owner and strengthens security.", "cancel": "Cancel", "sendLink": "Send link", "sending": "Sending...", "successSent": "<PERSON><PERSON> sent successfully!", "resetSent": "A secure reset link has been sent to", "checkInbox": "Check your inbox and click the link to create a new password.", "linkExpires": "The link will expire in 1 hour for your security.", "troubleshoot": "Don't see the email?", "checkSpam": "• Check your spam/junk folder", "waitTime": "• Wait a few minutes, delivery may take time", "retryLater": "• You can close this window and try again later"}, "tools": "Tools", "translationMethod": "Translation Method", "gpt4Description": "GPT-4: Higher quality translations, slower response", "azureDescription": "Azure: Faster translations, standard quality", "profileUpdated": "Profile Updated", "translationMethodUpdated": "Translation method preference saved successfully", "updateError": "Failed to update profile", "logout": {"confirm": "Do you really want to log out?", "button": "Log out", "success": "Successfully logged out", "error": "Error during logout"}, "filters": {"all": "All", "sections": {"all": "All", "listening": "Listening", "reading": "Reading", "writing": "Writing", "speaking": "Speaking"}, "status": {"all": "All", "completed": "Completed", "inProgress": "In Progress", "free": "Free", "premium": "Premium"}, "sortBy": {"dateDesc": "Date (recent)", "dateAsc": "Date (old)", "scoreDesc": "Score (high)", "scoreAsc": "Score (low)", "section": "Section", "testId": "Test number"}, "placeholders": {"search": "Search for a test...", "section": "Section", "status": "Status", "sortBy": "Sort by", "perPage": "Per page"}}, "unauthorized": {"title": "Unauthorized Access", "message": "You must be logged in to access this page."}}, "membership": {"title": "Premium Memberships", "subtitle": "Access all TCF Canada tests and maximize your success chances", "currentStatus": "Current Status", "lifetimeSubscription": "Lifetime Subscription", "activeSubscription": "Active Subscription", "expiredSubscription": "Expired Subscription", "freeAccount": "Free Account", "lifetimeMessage": "You have a lifetime subscription! You have access to all premium features permanently.", "activeMessage": "Your premium subscription expires on", "expiredMessage": "Your subscription has expired. Renew to continue accessing all tests.", "freeMessage": "You are currently using the free version. Upgrade to premium for full access.", "featuresTitle": "What's included in Premium membership?", "choosePlan": "Choose your plan", "mostPopular": "Most popular", "duration": {"month": "month", "year": "year", "lifetime": "lifetime"}, "buttons": {"subscribe": "Subscribe", "becomeMember": "Become member", "changePlan": "Change plan", "renew": "<PERSON>w", "upgradeToLifetime": "Upgrade to lifetime"}, "lifetimeThankYou": {"title": "Thank you for your trust!", "message": "Your lifetime subscription gives you access to all premium features permanently.", "startTests": "Start tests", "viewProfile": "View my profile"}, "promoSection": {"title": "Have a promotional code?", "description": "Use your promo code to get free access to the platform", "button": "Use a promo code"}, "freeFeatures": {"title": "Free features", "list": ["Access to free tests", "Basic progress tracking", "Results review"]}, "premiumFeatures": {"unlimited": "Unlimited tests", "mockExams": "Mock exams", "allLevels": "All levels (A1-C2)", "detailedResults": "Detailed results", "progressTracking": "Advanced progress tracking", "prioritySupport": "Priority support"}, "loginRequired": "You must log in to subscribe", "features": {"readingComprehension": {"title": "Reading Comprehension", "description": "Master reading skills with varied texts and difficulty levels"}, "listeningComprehension": {"title": "Listening Comprehension", "description": "Improve listening skills with authentic audio materials"}, "writtenExpression": {"title": "Written Expression", "description": "Develop writing skills with guided exercises and feedback"}, "oralExpression": {"title": "Oral Expression", "description": "Practice speaking with interactive exercises and examples"}, "accessAllTests": "Access to all tests", "progressTracking": "Progress tracking", "detailedCorrections": "Detailed corrections", "prioritySupport": "Priority support", "mockExams": "Mock exams", "noRenewal": "No renewal needed", "exclusiveAccess": "Exclusive access to new tests"}, "plans": {"week": {"name": "Weekly", "duration": "week"}, "monthly": {"name": "Monthly", "duration": "month"}, "quarterly": {"name": "Quarterly", "duration": "3 months"}, "yearly": {"name": "1 Year", "duration": "12 months"}, "lifetime": {"name": "Lifetime", "duration": "lifetime"}}, "freeAccountFeatures": {"title": "With free account", "list": ["Access to free tests", "Personal notes creation", "Basic progress tracking", "Community support"], "description": "Perfect for discovering the platform and starting your preparation!"}, "errors": {"stripeNotConfigured": "Stripe configuration error", "stripeKeyMissing": "Stripe key not configured", "paymentSessionError": "Error creating payment session", "paymentError": "Error", "loginRequiredTitle": "Login required", "loginRequiredMessage": "You must log in to subscribe"}}, "auth": {"logout": {"success": "You have been logged out successfully", "error": "Error during logout"}, "session": {"invalidated": {"title": "Account Security Alert", "deviceAccess": "Your account was accessed from another device.", "deviceAccessWithTime": "Your account was accessed from another device at {{time}}.", "loggedOut": "For your security, you have been logged out.", "redirecting": "Redirecting to login page in {{seconds}} seconds...", "clickToLogin": "Click this notification to go to login now.", "loginSuccess": "Login Successful", "loginSuccessMessage": "Welcome back! Your session is now active.", "logoutSuccess": "Logged Out", "logoutSuccessMessage": "You have been successfully logged out."}}, "login": {"title": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "createAccount": "Create account", "continueWithGoogle": "Continue with Google", "orDivider": "or", "usernameEmail": "Username or Email", "usernamePlaceholder": "<NAME_EMAIL>", "password": "Password", "passwordPlaceholder": "Your password", "forgotPassword": "Forgot password?", "signIn": "Sign in", "errors": {"usernameRequired": "Username or email required", "passwordMinLength": "Password must be at least 6 characters", "invalidCredentials": "Incorrect username/email or password", "emailNotVerified": "Email not verified", "verifyEmailMessage": "Please verify your email before signing in.", "googleSignInFailed": "Google sign-in failed. Please try again."}}, "register": {"title": "Create your account", "username": "Username", "usernamePlaceholder": "Enter your username", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm password", "confirmPasswordPlaceholder": "Confirm your password", "createAccount": "Create account", "updateRegistration": "Update registration", "haveAccount": "Already have an account?", "signIn": "Sign in", "continueWithGoogle": "Continue with Google", "orDivider": "Or continue with email", "fromVerification": "You can update your registration details below or continue with the existing verification.", "pendingExists": "You already have a pending registration for {{email}}.", "goToVerification": "Go to verification", "success": {"title": "Registration successful", "created": "Please check your email for verification code.", "updated": "Registration updated. Please check your email for the new verification code."}, "errors": {"usernameMinLength": "Username must be at least 3 characters", "emailInvalid": "Invalid email address", "passwordMinLength": "Password must be at least 6 characters", "passwordsDontMatch": "Passwords don't match", "googleSignInFailed": "Google sign-in failed. Please try again.", "usernameConflict": "Username already taken", "usernameConflictMessage": "This username is already being used by another pending registration. Please choose a different username."}}, "forgotPassword": {"title": "Reset Password", "description": "Password Recovery", "instructions": "Enter your email address and we'll send you a secure link to reset your password.", "emailLabel": "Email address", "emailPlaceholder": "<EMAIL>", "tips": {"title": "Helpful tips", "spam": "• Check your spam folder if you don't receive the email", "expires": "• The link will be valid for 1 hour", "correctEmail": "• Make sure to use your account email"}, "cancel": "Cancel", "sendLink": "Send link", "sending": "Sending...", "success": {"title": "Email sent", "message": "If the email exists, a reset link has been sent."}, "error": {"title": "Error", "message": "Error sending email. Please try again."}}, "emailVerification": {"title": "Email Verification", "checking": "Verifying your email...", "success": {"title": "Email verified successfully!", "message": "Your account has been activated. You can now sign in."}, "error": {"title": "Verification failed", "message": "The verification link is invalid or has expired."}, "goToLogin": "Go to login"}, "resetPassword": {"title": "Create New Password", "newPassword": "New password", "confirmPassword": "Confirm new password", "updatePassword": "Update password", "updating": "Updating...", "success": {"title": "Password updated", "message": "Your password has been updated successfully."}, "error": {"title": "Error", "message": "Error updating password. The link may have expired."}, "errors": {"passwordMinLength": "Password must be at least 6 characters", "passwordsDontMatch": "Passwords don't match"}}}, "contact": {"title": "Contact Support", "yourInfo": "Your information (automatically included)", "describeProblem": "Describe your problem", "placeholder": "Describe your problem in detail... The more information you provide, the better we can help you.", "tips": {"title": "Tips for effective support", "step1": "Describe the steps that led to the problem", "step2": "Mention the browser you are using", "step3": "Attach screenshots if necessary", "step4": "We will respond within 24-48 hours by email"}, "cancel": "Cancel", "send": "Send message", "sending": "Sending...", "success": {"title": "Message sent", "message": "Your message has been sent successfully. We will respond within 24-48 hours."}, "error": {"title": "Error", "message": "Error sending message. Please try again."}, "validation": {"minLength": "Message must contain at least 10 characters"}}, "collection": {"title": "My Collection", "questionsCount_one": "{{count}} question saved", "questionsCount_other": "{{count}} questions saved", "empty": {"title": "Your collection is empty", "message": "Start saving questions by clicking the star icon during your reading and listening tests.", "startReading": "Start a reading test"}, "search": "Search by test or question number...", "sections": {"reading": "READING", "listening": "LISTENING"}, "practice": {"checkAnswer": "Check answer", "tryAgain": "Try again", "selectQuestion": "Select a question above to start practicing", "showTranscript": "Show original text", "hideTranscript": "Hide original text", "showAllText": "Show all text", "hideAllText": "Hide all text"}, "notifications": {"removed": {"title": "Question removed", "message": "The question has been removed from your collection"}, "selectAnswer": {"title": "Warning", "message": "Please select an answer before checking."}}, "errors": {"loginRequired": "You must be logged in to access your collection.", "loadingError": "Error loading your collection. Please try again.", "noData": "Question data not available for question"}, "audio": {"error": {"title": "Audio Error"}, "premiumRequired": {"title": "Premium Membership Required", "message": "This audio is from premium content. You need an active membership to access premium audio files.", "action": "Upgrade to premium to access all audio content in your collection."}, "membershipExpired": {"title": "Membership Expired", "message": "Your premium membership has expired. You can no longer access premium audio content.", "action": "Renew your membership to continue accessing premium audio files."}, "upgradeButton": "Upgrade to Premium"}}, "mockExam": {"title": "<PERSON><PERSON>", "subtitle": {"listening": "Listening Comprehension", "reading": "Reading Comprehension", "writing": "Written Expression", "speaking": "Oral Expression"}, "backToTests": "Back to Tests", "finishExam": "Finish <PERSON><PERSON>", "loading": "Loading mock exam...", "errors": {"loadFailed": "Unable to load mock exam.", "dataNotFound": "Data not found", "tryAgain": "Please try again.", "noQuestions": "No questions found for this mock exam.", "questionNotFound": "Question {number} not found.", "returnToFirst": "Return to question 1"}, "results": {"completed": "Mock exam completed!", "score": "You got {{score}} points out of {{total}} questions.", "tcfScore": "TCF Score (out of 699): {{score}} / 699 ({{percent}}%)", "correctAnswers": "Correct answers: {{count}}", "incorrectQuestions": "Incorrect questions:", "question": "Q{{number}}", "yourAnswer": "Your answer: {{answer}}", "correctAnswer": "Correct answer: {{answer}}", "submitError": "Error submitting mock exam."}}, "test": {"sections": {"listening": "Listening Comprehension", "reading": "Reading Comprehension", "writing": "Written Expression", "speaking": "Oral Expression"}, "free": "Free", "submit": "Submit", "save": "Save", "question": "Question", "of": "of", "loading": {"totalQuestions": "Total questions:", "currentQuestion": "Current question:", "test": "Test", "section": "Section:", "returnToQuestion": "Return to question", "data": "Data:", "urlStructure": "Expected URL structure:", "forTests": "For tests:", "forLevels": "For levels:", "returnHome": "Return home", "viewTests": "View tests", "practiceByLevel": "Practice by Level", "oralComprehension": "Listening Comprehension", "writtenComprehension": "Reading Comprehension", "writtenExpression": "Written Expression", "oralExpression": "Oral Expression", "mockExam": "<PERSON><PERSON>", "returnTests": "Back to Tests", "questionNavigation": "Question Navigation", "answered": "Answered", "wrongAnswer": "Wrong answer", "unanswered": "Not answered", "question": "Question", "appearedIn": "Appeared in:", "browserNotSupported": "Your browser doesn't support the audio element.", "hideOriginalText": "Hide original text", "showOriginalText": "Show original text", "hideAllText": "Hide all text", "showAllText": "Show all text", "previous": "Previous", "next": "Next", "submitTest": "Submit test"}, "error": "Loading error", "mockExam": "<PERSON><PERSON>", "groupTest": "Level Test", "graded": {"title": "Previously graded test", "correct": "correct answers,", "incorrect": "errors"}, "saved": {"position": {"warning": "Saved current_question", "clamped": "is out of bounds. Clamped to"}}, "resumed": {"title": "Test resumed", "message": "You continue from question"}, "submitted": {"score": "You scored", "out": "points out of", "questions": "questions", "tcf": "TCF Score (out of 699):", "correct": "Correct answers:", "incorrect": "Incorrect questions", "question": "Q", "yourAnswer": "Your answer:", "correctAnswer": "Correct answer:", "error": "Error submitting test."}, "gradingResults": {"title": "Test Results", "correct": "Correct", "outOf": "out of", "questions": "questions", "tcfScore": "TCF Score", "tcfLevel": "TCF Level", "correctAnswers": "Correct", "incorrectAnswers": "Incorrect", "reviewIncorrect": "Review Incorrect Questions", "question": "Question", "yourAnswer": "Your answer", "correctAnswer": "Correct answer", "saveToCollection": "Save to Collection Book", "saveToCollectionDescription": "Save all {{count}} incorrect questions to your collection for later review and practice.", "addToCollectionButton_one": "Add 1 Question to Collection", "addToCollectionButton_other": "Add {{count}} Questions to Collection", "addingToCollection": "Adding to Collection...", "addedToCollection": "Added to Collection!", "questionsAdded": "questions added to your collection!", "questionsAlreadySaved": "questions were already saved.", "allAlreadyInCollection": "All questions were already in your collection.", "someIssues": "Some issues occurred:", "viewCollection": "View Collection", "closeButton": "Continue", "collectionNotAvailable": "Collection is not available for this test type yet.", "errors": {"noValidQuestions": "No questions with valid database IDs found for collection. This may be because this test type doesn't support collection yet.", "failedToAdd": "Failed to add questions to collection. ", "tryAgain": "Please try again or contact support if the problem persists."}, "removeCorrectFromCollection": "Remove all correct answers from collection"}, "noQuestionsFound": "No questions found for this test.", "questionNotFound": "Question", "features": {"hint": {"title": "Discover our tools!", "analysis": "Analysis", "notebook": "Notes", "description": "Click the buttons on the sides to access the tools"}}}, "payments": {"success": {"title": "Payment Successful!", "description": "Your subscription has been successfully activated. You now have access to all premium tests.", "membershipUpdated": "Your subscription has been automatically updated", "sessionId": "Session ID: {{sessionId}}", "backToHome": "Back to Home", "viewProfile": "View My Profile"}, "cancel": {"title": "Payment Cancelled", "description": "Your payment has been cancelled. No charge has been made to your account.", "tryAgainInfo": "You can retry the payment at any time or explore our free tests.", "backToHome": "Back to Home", "tryAgain": "Try Again"}}, "redeemPromo": {"title": "Use a Promo Code", "description": "Enter your promo code to get free premium access", "label": "Promo code", "placeholder": "e.g. WELCOME3", "submit": "Use Code", "submitting": "Applying...", "success": "Promo code applied!", "redirecting": "Redirecting to your profile in a few seconds...", "error": "Error", "enterCode": "Please enter a promo code", "loginRequired": "<PERSON><PERSON> Required", "mustBeLoggedIn": "You must be logged in to use a promo code.", "login": "Log in", "howItWorks": "How it works?", "rule1": "Each promo code gives you free premium access days", "rule2": "Days are added to your current expiration date", "rule3": "Each code can only be used once per user", "rule4": "Maximum 3 promo codes per account", "backToMembership": "Back to Memberships", "myProfile": "My Profile", "usedCodes": "Already used codes", "yourStatus": "Your Status", "codesUsed": "Codes used"}, "resetPassword": {"title": "Reset Password", "subtitle": "Enter your new password", "newPassword": "New password", "confirmPassword": "Confirm password", "newPasswordPlaceholder": "Your new password", "confirmPasswordPlaceholder": "Confirm your new password", "submit": "Reset Password", "success": "Success", "successMessage": "Your password has been successfully reset.", "invalidLink": "Invalid reset link. Please request a new link.", "missingToken": "Missing reset token", "error": "Error during password reset", "validation": {"minLength": "Password must contain at least 6 characters", "noMatch": "Passwords do not match"}}, "authCallback": {"error": "Error retrieving session", "noToken": "No access token found", "authFailed": "Authentication failed", "backToLogin": "Back to Login"}, "emailVerification": {"title": "Email Verification", "subtitle": "Enter the verification code sent to your email address", "missingEmail": "Email missing. Please register again.", "backToRegister": "Back to registration", "codeLabel": "Verification code", "codeSent": "A 6-digit verification code has been sent to:", "codeValid": "Code valid for:", "codeExpired": "The code has expired. Please request a new one.", "verifyButton": "Verify my email", "noCodeReceived": "Didn't receive the code?", "resendCode": "Resend code", "canResendIn": "You can request a new code in {{time}}", "tips": {"title": "Tips:", "checkSpam": "Check your spam/junk folder", "validFor": "Code is valid for 15 minutes", "caseSensitive": "Codes are case sensitive"}, "wrongEmail": "Wrong email address?", "registerAgain": "Register again", "success": {"title": "Email verified!", "message": "Your account has been successfully activated."}, "error": {"title": "Error", "invalidCode": "Invalid verification code", "resendFailed": "Error sending code"}, "resend": {"title": "Code resent", "message": "A new verification code has been sent."}}, "speakingTask2": {"title": "Simulation Parties - Oral Expression", "subtitle": "Task 2", "backToSpeaking": "Back to Oral Expression", "parties": "Parties", "scenarios": "Scenarios", "instruction": "For each partie, carefully read the presented situation, then use the example questions to guide your conversation. These questions will help you develop a natural and coherent dialogue.", "freePreview": "You are currently viewing the free preview with basic parties.", "premiumRequired": "Detailed example questions require a premium subscription for full access.", "scenario": "<PERSON><PERSON><PERSON> {{number}}", "getFullAccess": "Get full access to detailed questions to guide your conversation practice.", "dataNotFound": "Data not found for this month", "loadingError": "Error loading data", "dataUnavailable": "Data unavailable", "exampleQuestions": "Example questions:"}, "readingListeningTests": {"guide": {"reading": {"title": "Reading Test Guide", "overview": {"title": "Test Overview", "format": "Format: 39 multiple-choice questions (one correct answer)", "duration": "Duration: 60 minutes", "content": "Covers texts ranging from short notices to longer complex passages with gradually increasing difficulty"}, "features": {"title": "Features", "highlight": "Highlight Function", "highlightExplanation": "Click and drag to highlight important text passages for better focus", "notebook": "Notebook Function", "notebookExplanation": "Take notes during your practice to track key insights and strategies", "collection": "Collection Book", "collectionExplanation": "Save challenging questions to review later and track your progress", "verified": "Verified Answers (controversial ones flagged)", "verifiedExplanation": "All answers verified by experts, with controversial questions clearly marked", "optimized": "Optimized Practice (deduplicated/classified)", "optimizedExplanation": "Deduplicated and classified questions for efficient study sessions", "translation": "Translation", "translationExplanation": "Translate text passages to English or Chinese for better comprehension", "mockExams": "Mock exams with randomly generated tests", "mockExamsExplanation": "Generate random 39-question exams that simulate real test conditions"}}, "listening": {"title": "Listening Test Guide", "overview": {"title": "Test Overview", "format": "Format: 39 multiple-choice questions (one correct answer)", "duration": "Duration: 35 minutes; audio played only once", "content": "Includes dialogues, announcements, interviews—all at natural pace"}, "features": {"title": "Features", "audio": "Enhanced Audio Quality", "audioExplanation": "Crystal clear audio quality optimized for language learning", "transcript": "Transcript Function for sentence-by-sentence review", "transcriptExplanation": "Review audio sentence by sentence with synchronized transcripts", "notebook": "Notebook Function", "notebookExplanation": "Take notes while listening to improve comprehension strategies", "collection": "Collection Book", "collectionExplanation": "Bookmark difficult questions for targeted practice sessions", "verified": "Verified Answers (flagged if debatable)", "verifiedExplanation": "Expert-verified answers with debatable questions clearly flagged", "optimized": "Optimized Practice", "optimizedExplanation": "Streamlined practice with organized and categorized content", "translation": "Translation", "translationExplanation": "Translate transcript text to English or Chinese for better understanding", "mockExams": "<PERSON><PERSON> with random questions", "mockExamsExplanation": "Create randomized 39-question mock exams for realistic practice"}}, "writing": {"title": "Writing Test Guide", "overview": {"title": "Test Overview", "format": "Format: 3 tasks, 60 minutes total", "tasks": {"title": "Task Types", "task1": "Message/email (60–120 words)", "task2": "Short narrative (~120–150 words)", "task3": "Argumentative text (~120–180 words)"}}, "features": {"title": "Features", "collection": "Full Task & Correction Collection", "practice": "Timed Practice (<PERSON><PERSON>)", "guidance": "Writing Guidance"}}, "speaking": {"title": "Speaking Test Guide", "overview": {"title": "Test Overview", "duration": "Total Duration: ~12 minutes (including ~2 min prep)", "tasks": {"title": "3 Tasks", "task1": "Structured interview (~2 min, no prep)", "task2": "Interactive conversation (2 min prep + ~3.5 min exchange)", "task3": "Argumentative monologue (~4.5 min, no prep)"}}, "features": {"title": "Features", "collection": "Comprehensive Task Collection", "practice": "<PERSON><PERSON> Practice Sessions", "guides": "Structure Guides"}}}, "sections": {"listening": {"title": "Listening Comprehension", "description": "Test your ability to understand spoken French."}, "reading": {"title": "Reading Comprehension", "description": "Test your ability to understand written French."}, "writing": {"title": "Written Expression", "description": "Test your ability to express yourself in written French."}, "speaking": {"title": "Oral Expression", "description": "Test your ability to express yourself in spoken French."}}, "levels": {"a1": "Beginner level", "a2": "Elementary level", "b1": "Intermediate level", "b2": "Upper intermediate level", "c1": "Advanced level", "c2": "Mastery level", "intermediate": "Intermediate", "intermediateAdvanced": "Intermediate +", "advanced": "Advanced"}, "breadcrumb": {"home": "Home"}, "sectionTitles": {"freeTests": "Free Tests ({{count}})", "allTests": "All Tests ({{count}})", "practiceByLevel": "Practice by Level"}, "practiceByLevel": {"infoMessage": "Practice by level contains deduplicated and classified questions from all tests. Each level covers specific question ranges (A1 to C2), allowing you to focus on your target difficulty level."}, "mockExams": {"title": "<PERSON><PERSON>", "subtitle": "Examens Simulés TCF Canada", "description": "Prepare with comprehensive exams of 39 random questions", "createExam": "Create Exam", "aboutTitle": "About <PERSON><PERSON>", "aboutDescription": "Mock exams contain 39 random questions from deduplicated group tests. Each exam follows the official TCF Canada structure with questions from different levels (A1 to C2). You can have maximum {{maxAllowed}} mock exams at the same time.", "premiumRequired": "Mock exams are reserved for Premium members.", "premiumDescription": "They offer a complete experience with 39 random questions", "noExamsTitle": "No mock exams created", "noExamsDescription": "Click \"Create Exam\" to get started", "examCount": "{{count}} / {{max}} mock exams", "createAccount": "Create a free account to access tests and track your progress.", "createdOn": "Created on {{date}}", "completed": "Completed", "randomQuestions": "39 random questions", "recreate": "Recreate with new questions", "recreateTooltip": "Recreate with new questions", "deleteTooltip": "Delete"}, "errors": {"loadTests": "Failed to load tests. Please try again later.", "loadMockExams": "Unable to load mock exams", "limitReached": "Limit reached", "maxExamsReached": "You can only have a maximum of {{maxAllowed}} mock exams. Delete one to create a new one.", "createMockExam": "Error creating mock exam", "deleteMockExam": "Unable to delete mock exam", "recreateMockExam": "Error recreating mock exam", "examExists": "Exam already exists", "examExistsMessage": "The exam {{examName}} already exists. You can delete it from the list below and try again, or contact us if the problem persists.", "loadingTitle": "Loading error"}, "success": {"created": "Success", "createdMessage": "{{examName}} created with {{questionCount}} questions", "deleted": "Deleted", "deletedMessage": "Mock exam deleted successfully", "recreated": "Recreated successfully", "recreatedMessage": "{{examName}} recreated with {{questionCount}} new questions", "historyDeleted": "History deleted", "historyDeletedMessage": "Test history has been deleted successfully. You can now restart the test.", "levelHistoryDeleted": "Level {{level}} test history has been deleted successfully. You can now restart the test."}, "access": {"limitedTitle": "Limited Access", "freeOnly": "You have access to free tests only. ", "expiredSubscription": "Your subscription has expired. Renew to access all tests. ", "loadingTitle": "Loading progress data...", "loadingMessage": "Basic tests are displayed. Loading progress and history data in progress...", "becomePremium": "Become Premium Member", "renewSubscription": "Renew Subscription"}, "testCard": {"completedGraded": "Completed and graded", "completed": "Completed", "progress": "{{percent}}% completed", "readyToStart": "Ready to start", "subscriptionRequired": "Subscription required", "questions": "{{count}} questions", "results": "Results", "continue": "Continue", "start": "Start", "resetTitle": "Reset test", "resetMessage": "Are you sure you want to delete all history for this test?", "resetWarning": "This action is irreversible and you will have to restart the test from the beginning.", "resetResultsWarning": "Warning: You will lose your current results: {{score}}/{{maxScore}} ({{correct}} correct, {{wrong}} wrong answers)", "resetLevelTitle": "Reset level test", "resetLevelMessage": "Are you sure you want to delete all history for this level test?", "resetLevelWarning": "This action is irreversible and you will have to restart the test from the beginning.", "resetLevelResultsWarning": "Current results: {{score}}/{{maxScore}} ({{correct}} correct, {{wrong}} wrong answers)"}, "callToAction": {"title": "Log in to get started", "subtitle": "Create an account or log in to access all tests and track your progress.", "register": "Create account", "login": "Log in"}, "membershipUpgrade": {"title": "Unlock Premium Features", "subtitle": "Upgrade to premium membership to access all tests, detailed corrections, and advanced features.", "upgrade": "Upgrade to Premium", "viewProfile": "View Profile"}, "backToSections": "← Back to Sections", "auth": {"loginRequired": "You need to log in first to access tests."}}, "speakingTask3": {"title": "Opinion Questions - Oral Expression", "subtitle": "Task 3", "backToSpeaking": "Back to Oral Expression", "wordsPerResponse": "Words/response", "instruction": "Read each question carefully and think about your personal response.", "correctionAvailable": "Click \"Correction\" to see an exemplary answer that will help you structure your argumentation.", "correctionPremium": "Exemplary answers are available with a premium subscription.", "freePreview": "You are currently viewing the free preview with basic questions.", "premiumRequired": "Detailed exemplary answers require a premium subscription for full access.", "exemplaryAnswer": "Exemplary answer:", "correction": "Correction", "getFullAccess": "Get full access to detailed answers to improve your argumentation.", "dataNotFound": "Data not found for this topic", "loadingError": "Error loading data", "dataUnavailable": "Data unavailable", "questions": "Questions", "words": "words", "paragraphs": "paragraphs", "task": "Task", "question": "Question:", "hideCorrection": "Hide correction", "exemplaryAnswerAvailable": "Exemplary answer available with Premium:", "premium": "Premium", "instructionsTitle": "Instructions"}, "writingMonthDetails": {"task": "Task {{number}}", "tasks": "{{count}} task", "tasksPlural": "{{count}} tasks", "premiumAccess": "Premium Access activated!", "premiumMessage": "You have access to all tasks and corrections for this month.", "freeAccess": "Free access:", "freeMessage": "You can view all tasks for this month.", "combinations": "Combinations", "tasksLabel": "Tasks", "monthDescription": "This month contains {{combinationCount}} combinations with a total of {{taskCount}} writing tasks.", "combinationInfo": "Each combination includes 3 different tasks. Detailed corrections are available with a Premium subscription.", "loginForCorrections": "Log in and get a Premium subscription to see corrections.", "upgradeForCorrections": "Get a Premium subscription to see corrections.", "errors": {"missingMonthId": "Month ID missing", "loadingData": "Error loading month data", "monthNotFound": "Month not found", "title": "Error"}, "navigation": {"backToExercises": "Back to Exercises"}}, "speaking": {"title": "Oral Expression", "subtitle": "Master oral expression with practical scenarios and opinion questions", "allTasks": "All Tasks", "accessTitle": "Content Access", "freePreview": "Free preview: Basic scenarios and questions available for everyone.", "premiumContent": "Premium content: Detailed example questions and exemplary answers require a subscription.", "access": {"premiumActive": "Premium Access activated!", "premiumMessage": "You have access to all speaking tasks with detailed example questions and exemplary answers.", "freeAccess": "Free access:", "freeMessage": "You can see all speaking tasks.", "loginForPremium": "Log in and get a Premium subscription to access detailed content.", "upgradeForPremium": "Get a Premium subscription to access detailed content."}, "sections": {"allTasks": "All Tasks ({{count}} items)"}, "noTasks": {"title": "No tasks available", "message": "No speaking tasks are currently available. Please try again later."}, "backToSections": "← Back to Sections", "task2": {"title": "Task 2", "subtitle": "Simulation Scenarios", "description": "Practice realistic communication situations with example questions to guide your conversation.", "monthsCount": "{{count}} months", "parties": "{{count}} parts", "scenarios": "{{count}} scenarios", "exampleQuestions": "Example questions", "total": "{{count}} total", "premiumRequired": "Premium required", "fullAccess": "Full access", "previewAvailable": "Preview available", "explore": "Explore"}, "task3": {"title": "Task 3", "subtitle": "Opinion Questions", "description": "Develop your arguments on various topics with detailed exemplary answers.", "subjectsCount": "{{count}} subjects", "questions": "{{count}} questions", "exemplaryAnswers": "Exemplary answers", "available": "Available", "premiumRequired": "Premium required", "fullAccess": "Full access", "questionsAvailable": "Questions available", "explore": "Explore"}, "statistics": {"title": "Global Statistics", "monthsAvailable": "Months available", "scenariosTask2": "Task 2 Scenarios", "subjectsAvailable": "Subjects available", "questionsTask3": "Task 3 Questions"}, "errors": {"loadingData": "Error loading oral expression data"}}, "notebook": {"placeholder": "Your notes...", "title": "My Notebooks", "description": "Browse and manage your saved notebook entries", "createNew": "Create New", "createFirst": "Create Your First Notebook", "searchPlaceholder": "Search notebooks...", "empty": "No notebooks saved yet", "noSearchResults": "No notebooks found matching your search", "universal": "Universal", "characters": "characters", "open": "Open", "delete": "Delete", "lastUpdated": "Last updated", "confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete this notebook? This action cannot be undone.", "deleteSuccess": "Notebook deleted", "deleteSuccessMessage": "Your notebook has been deleted successfully", "deleteError": "Delete failed", "deleteErrorMessage": "Failed to delete notebook", "loadError": "Failed to load notebooks", "loadErrorMessage": "There was an error loading your notebooks. Please try again.", "loginMessage": "Please log in to view your saved notebooks.", "saveAs": "Save as...", "saveAsDescription": "Give your notebook a name to save it. Your current global notebook will be cleared.", "notebookName": "Notebook Name", "namePlaceholder": "Enter notebook name...", "save": "Save", "nameRequired": "Please enter a notebook name", "saveSuccess": "Notebook Saved", "saveSuccessMessage": "Notebook has been saved successfully", "saveError": "Save Failed", "saveErrorMessage": "Failed to save notebook", "nameExists": "A notebook with this name already exists", "noContent": "No content to save", "emptyContent": "Cannot save empty notebook", "error": "Error", "edit": "Edit", "editNotebook": "Edit Notebook", "editDescription": "Edit your notebook content below. Changes will be saved automatically.", "saveChanges": "Save Changes", "updateSuccess": "Notebook updated", "updateSuccessMessage": "Your notebook has been updated successfully", "updateError": "Update failed", "updateErrorMessage": "Failed to update notebook", "deleteModal": {"title": "Delete all notes", "message": "Are you sure you want to delete all your notes? This action is irreversible.", "cancel": "Cancel", "confirm": "Delete all"}}, "modificationNotification": {"answerUpdates": "Answer Updates ({{count}})", "listeningModifications": "Listening Answer Modifications", "readingModifications": "Reading Answer Modifications", "summaryMessage": "Some test answers have been reviewed and updated for accuracy. Please check the modification log for details.", "recentChanges": "Recent Changes:"}, "analysis": {"title": "Analysis", "notAvailable": "No analysis available for this question", "noContent": "No analysis content available in this language"}, "downloads": {"title": "FREE Study Materials", "subtitle": "Download comprehensive TCF Canada preparation materials at no cost", "description": "Access our complete collection of study guides, practice tests, and reference documents. All materials are available through Google Drive and Baidu Wangpan for your convenience.", "downloadButton": "Download", "loginRequired": {"title": "<PERSON><PERSON> Required", "message": "You must be logged in to access the downloads page.", "button": "Go to Login"}, "categories": {"listening": {"title": "Listening Materials", "description": "Audio files, transcripts, and practice tests for listening comprehension preparation."}, "reading": {"title": "Reading Materials", "description": "Text passages, comprehension exercises, and strategy guides for reading skills development."}, "writing": {"title": "Writing Materials", "description": "Task examples, essay prompts, and structured writing practice exercises for TCF Canada.\n\nBaidu Wangpan extraction code: chez"}, "speaking": {"title": "Speaking Materials", "description": "Conversation prompts, speaking task scenarios, and oral expression practice guides.\n\nBaidu Wangpan extraction code: chez"}}, "info": {"title": "Download Information", "description": "All materials are regularly updated and completely free. Choose your preferred download platform below. Google Drive offers easy preview and sharing, while Baidu Wangpan provides faster downloads for users in China.", "lastUpdated": "Last updated"}}, "translation": {"title": "Translation", "originalText": "Original text", "selectLanguage": "Select a language to translate", "copy": "Copy translation", "copied": "Copied!", "copiedToClipboard": "Translation copied to clipboard", "error": "Translation Error", "failed": "Translation failed", "networkError": "Network error occurred", "copyFailed": "Failed to copy to clipboard", "translate": "Translate"}, "writing": {"title": "Written Expression", "subtitle": "Complete collection of writing exercises organized by month. Tasks are free, corrections require a Premium subscription.", "navigation": {"title": "Writing Tests", "subtitle": "Monthly exercises", "expand": "Expand navigation", "collapse": "Collapse navigation", "allTests": "All Tests", "bookmarked": "Bookmarked", "loading": "Loading...", "openMenu": "Open writing navigation", "current": "Current", "previous": "Previous", "next": "Next", "previousMonth": "Previous month", "nextMonth": "Next month", "navigateToMonth": "Navigate to {{month}}", "tasksCount": "{{count}} task", "tasksCount_plural": "{{count}} tasks", "allMonths": "All Months"}, "monthCard": {"combinations": "{{count}} combinations", "tasks": "{{count}} tasks", "content": "Content", "total": "{{count}} total", "readyToUse": "Ready to use", "explore": "Explore"}, "access": {"premiumActive": "Premium Access activated!", "premiumMessage": "You have access to all writing tasks and their detailed corrections.", "freeAccess": "Free access:", "freeMessage": "You can see all writing tasks.", "loginForCorrections": "Log in and get a Premium subscription to access detailed corrections.", "upgradeForCorrections": "Get a Premium subscription to access detailed corrections."}, "statistics": {"months": "Months", "combinations": "Combinations", "tasks": "Tasks"}, "sections": {"allTasks": "All Tasks ({{count}} months)"}, "noExercises": {"title": "No exercises available", "message": "No writing exercises are currently available. Please try again later."}, "backToSections": "← Back to Sections", "taskDetails": {"task": "Task {{number}}", "viewDetails": "View details", "hide": "<PERSON>de", "correction": "Correction", "premiumRequired": "Premium required", "correctionLabel": "Correction:", "instructionLabel": "Instruction:", "correctionAvailable": "Correction available with Premium subscription", "combinationTask": "Combination {{combination}} - Task {{task}}", "writeResponse": "Write Response"}, "editor": {"taskPrompt": "Task", "words": "words", "placeholder": "Start writing your response here...", "loadingContent": "Loading your previous work...", "save": "Save Draft", "submit": "Submit Final", "saving": "Saving", "lastSaved": "Last saved", "unsaved": "Unsaved changes", "viewHistory": "View submission history", "submissionHistory": "Submission History", "final": "Final", "draft": "Draft", "submitted": "Submitted Successfully", "noSubmissions": "No submissions yet", "finalSubmissionExists": "Final submission completed", "finalSubmissionMessage": "You submitted your final answer on {{date}} with {{words}} words.", "saved": "Draft Saved", "savedMessage": "Your draft has been saved successfully.", "submittedMessage": "Your writing has been submitted successfully.", "error": "Error", "emptyContent": "Please write something before saving or submitting.", "saveError": "Failed to save your draft. Please try again.", "submitError": "Failed to submit your writing. Please try again.", "minWordsError": "You need at least {{min}} words. Currently: {{current}} words.", "maxWordsError": "You have exceeded the maximum of {{max}} words. Currently: {{current}} words.", "needMoreWords": "Need {{needed}} more words to reach minimum", "tooManyWords": "{{excess}} words over the maximum limit"}}, "error": {"audio": {"title": "Audio Error", "loading": "Failed to load audio file", "network": "Network error while loading audio", "notFound": "Audio file not found"}, "image": {"title": "Image Error", "loading": "Failed to load image file", "network": "Network error while loading image", "notFound": "Image file not found"}, "premium": {"required": "Premium Membership Required", "audio": "This audio content requires a premium membership to access.", "image": "This image content requires a premium membership to access.", "upgrade": "Upgrade to Premium"}}, "classifiedWriting": {"infoMessage": "Practice by tâche contains deduplicated and classified writing tasks from all tests. Each tâche covers specific task types and themes, allowing you to focus on your target writing skills.", "title": "Classified Tasks", "subtitle": "Explore writing tasks organized by themes and topics with intelligent deduplication", "loading": {"cards": "Loading classified writing tasks...", "overview": "Loading overview...", "topics": "Loading topics...", "tasks": "Loading tasks..."}, "errors": {"loadCards": "Failed to load classified writing cards", "loadOverview": "Error loading task overview", "loadTopics": "Error loading topic details", "loadTasks": "Error loading tasks", "search": "Error searching tasks"}, "noData": {"title": "No Data Available", "message": "No classified writing tasks are currently available."}, "card": {"totalTasks": "Total tasks", "uniqueTasks": "Unique tasks", "topics": "Topics", "deduplication": "Deduplication", "topTopics": "Top topics", "moreTopics": "more topics", "explore": "Explore", "premiumRequired": "Premium Required", "loginRequired": "<PERSON><PERSON> Required", "noTasks": "No tasks available"}, "taches": {"1": {"description": "Personal message writing (60-120 words)"}, "2": {"description": "Informative article writing (120-150 words)"}, "3": {"description": "Argumentative essay writing (120-180 words)"}}, "overview": {"title": "Overview", "description": "Select a theme to see available subtopics", "tasks": "tasks"}, "auth": {"loginRequired": "Login required to access classified tasks"}, "premium": {"title": "Premium Feature", "description": "Access over 1000 writing tasks organized by themes with intelligent deduplication. Perfect for targeted and efficient practice."}, "task": {"content": "Instructions", "wordLimit": "Words", "startWriting": "Start Writing", "membershipRequired": "Membership Required"}, "topics": {"recommendation": "Recommendation", "description_places": "Place Description", "description_person": "Person Description", "description_object": "Object Description", "narration": "Narration", "opinion": "Opinion", "explanation": "Explanation", "argumentation": "Argumentation", "comparison": "Comparison", "analysis": "Analysis"}, "navigation": {"expand": "Expand", "collapse": "Collapse"}, "subtopic": {"description": "Write a template response that covers this type of task"}, "template": {"label": "Template", "writeResponse": "Your template response", "description": "Write a response that covers this type of task. Your response will be applicable to all similar examples.", "viewExamples": "View task examples", "startWriting": "Write for this template", "save": "Save template", "saveSuccess": "Template saved successfully!", "saveError": "Error saving template", "placeholder": "Write your template response here. This response should cover the general pattern of this type of task...", "words": "words", "autoSaving": "Auto-saving...", "autoSaved": "Auto-saved", "autoSaveError": "Save error"}, "examples": {"title": "Task examples", "description": "Use these examples as reference to understand the pattern of this type of task.", "example": "Example", "count": "examples", "taskId": "Task identifier", "taskIds": "Task identifiers", "moreExamples": "... and {{count}} other similar examples", "noTasks": "No examples available"}}}