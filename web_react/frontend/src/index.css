@import "tailwindcss";

/* Dark Mode CSS Variables and Global Styles */
:root {
  /* Light theme colors */
  --tcf-bg-primary: #f7f9fb;
  --tcf-bg-surface: #ffffff;
  --tcf-bg-surface-hover: #f8f9fa;
  --tcf-text-primary: #212529;
  --tcf-text-secondary: #6c757d;
  --tcf-text-dimmed: #adb5bd;
  --tcf-border: #eaeaea;
  --tcf-border-light: #f1f3f4;
  --tcf-primary: #007bff;
  --tcf-primary-hover: #0056b3;

  /* Section colors */
  --tcf-listening: #007bff;
  --tcf-reading: #28a745;
  --tcf-writing: #fd7e14;
  --tcf-speaking: #dc3545;

  /* Optimized transition for smooth theme switching */
  --tcf-transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                    color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                    border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark theme colors */
:root.dark {
  --tcf-bg-primary: #000000;
  --tcf-bg-surface: #1a1a1a;
  --tcf-bg-surface-hover: #2d2d2d;
  --tcf-text-primary: #f8f9fa;
  --tcf-text-secondary: #adb5bd;
  --tcf-text-dimmed: #6c757d;
  --tcf-border: #404040;
  --tcf-border-light: #2d2d2d;
  --tcf-primary: #007bff;
  --tcf-primary-hover: #0056b3;

  /* Section colors (slightly adjusted for dark mode) */
  --tcf-listening: #1e90ff;
  --tcf-reading: #32cd32;
  --tcf-writing: #ff8c00;
  --tcf-speaking: #ff6b6b;
}

/* Optimized global styles with theme support */
html, body {
  background-color: var(--tcf-bg-primary);
  color: var(--tcf-text-primary);
  transition: var(--tcf-transition);
}

/* Prevent layout shifts by ensuring consistent box-sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Prevent layout shifts from border changes */
[class*="mantine-"] {
  box-sizing: border-box;
}

/* Apply transitions only to theme-related properties */
.theme-transition,
[data-theme-aware="true"] {
  transition: var(--tcf-transition);
}

/* Specific elements that should transition smoothly */
.mantine-AppShell-header,
.mantine-Card-root,
.mantine-Button-root,
.mantine-ActionIcon-root,
.mantine-Menu-dropdown,
.mantine-Text-root,
.mantine-Container-root,
.mantine-Paper-root,
.mantine-Accordion-root,
.mantine-Accordion-item,
.mantine-Accordion-control,
.mantine-Accordion-panel,
.mantine-SimpleGrid-root {
  transition: var(--tcf-transition) !important;
}

/* Prevent flash during theme switching */
.theme-switching * {
  transition: none !important;
}

/* Ensure immediate theme application and prevent layout shifts */
:root.dark,
:root:not(.dark) {
  transition: none;
}

/* Prevent cumulative layout shift during theme transitions */
.theme-switching {
  overflow: hidden;
}

.theme-switching * {
  transition: none !important;
  transform: none !important;
}

/* Force consistent theme transitions for common UI elements */
div, span, p, h1, h2, h3, h4, h5, h6,
[class*="mantine-"],
[data-theme-aware="true"] {
  transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Fix z-index issues with navigation elements */
.mantine-Tooltip-tooltip {
  z-index: 1100 !important;
}

.mantine-Menu-dropdown {
  z-index: 1050 !important;
}

/* Ensure Mantine components respect our theme */
.mantine-AppShell-root {
  background-color: var(--tcf-bg-primary) !important;
}

.mantine-AppShell-main {
  background-color: var(--tcf-bg-primary) !important;
}

/* Custom scrollbar for both themes to prevent layout shifts */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--tcf-bg-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--tcf-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--tcf-text-dimmed);
}

/* Ensure consistent scrollbar appearance across themes */
:root:not(.dark) ::-webkit-scrollbar-track {
  background: #f1f3f4;
}

:root:not(.dark) ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
}

:root:not(.dark) ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode support for Mantine components */
:root.dark .mantine-Card-root {
  background-color: var(--tcf-bg-surface) !important;
  border-color: var(--tcf-border) !important;
  color: var(--tcf-text-primary) !important;
}

:root.dark .mantine-Paper-root {
  background-color: var(--tcf-bg-surface) !important;
  border-color: var(--tcf-border) !important;
  color: var(--tcf-text-primary) !important;
}

:root.dark .mantine-Text-root {
  color: var(--tcf-text-primary) !important;
}

:root.dark .mantine-Title-root {
  color: var(--tcf-text-primary) !important;
}

/* Dark mode for modals and overlays */
:root.dark .mantine-Modal-content {
  background-color: var(--tcf-bg-surface) !important;
  color: var(--tcf-text-primary) !important;
}

:root.dark .mantine-Modal-header {
  background-color: var(--tcf-bg-surface) !important;
  border-bottom-color: var(--tcf-border) !important;
}

/* Dark mode for notifications */
:root.dark .mantine-Notification-root {
  background-color: var(--tcf-bg-surface) !important;
  border-color: var(--tcf-border) !important;
  color: var(--tcf-text-primary) !important;
}

/* Bookmark highlighting styles */
.bookmark-highlighted {
  position: relative;
}

.bookmark-highlighted::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.1));
  animation: bookmarkGlow 2s ease-in-out infinite alternate;
  z-index: -1;
}

@keyframes bookmarkGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
  }
}

/* Question card highlighting for bookmarked items */
.question-card-bookmarked {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02));
  border: 1px solid rgba(255, 193, 7, 0.2);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1);
  position: relative;
}

.question-card-bookmarked::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 193, 7, 0.8), rgba(255, 193, 7, 0.4));
  border-radius: 8px 8px 0 0;
}

/* Animation for newly bookmarked items */
.bookmark-success {
  animation: bookmarkSuccess 0.6s ease-out;
}

@keyframes bookmarkSuccess {
  0% {
    transform: scale(1);
    background-color: rgba(255, 193, 7, 0.1);
  }
  50% {
    transform: scale(1.02);
    background-color: rgba(255, 193, 7, 0.2);
  }
  100% {
    transform: scale(1);
    background-color: rgba(255, 193, 7, 0.05);
  }
}
