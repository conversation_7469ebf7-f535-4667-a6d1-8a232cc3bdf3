import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ThemeMode = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

interface ThemeState {
  mode: ThemeMode;
  resolvedTheme: ResolvedTheme;
  setMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
}

// Function to get system theme preference
const getSystemTheme = (): ResolvedTheme => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// Function to resolve theme based on mode
const resolveTheme = (mode: ThemeMode): ResolvedTheme => {
  if (mode === 'system') {
    return getSystemTheme();
  }
  return mode;
};

// Function to apply theme to document with optimized performance
const applyThemeToDocument = (theme: ResolvedTheme) => {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;

  // Add theme-switching class to prevent layout shifts
  root.classList.add('theme-switching');

  // Apply theme changes immediately for synchronous update
  if (theme === 'dark') {
    root.classList.add('dark');
    root.setAttribute('data-mantine-color-scheme', 'dark');
  } else {
    root.classList.remove('dark');
    root.setAttribute('data-mantine-color-scheme', 'light');
  }

  // Force immediate style recalculation
  root.offsetHeight;

  // Remove theme-switching class after a brief delay to allow transitions to resume
  setTimeout(() => {
    root.classList.remove('theme-switching');
  }, 50);
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      mode: 'system',
      resolvedTheme: 'light',
      
      setMode: (mode: ThemeMode) => {
        const resolvedTheme = resolveTheme(mode);
        applyThemeToDocument(resolvedTheme);
        set({ mode, resolvedTheme });
      },
      
      toggleTheme: () => {
        const currentMode = get().mode;
        const currentResolved = get().resolvedTheme;
        
        let newMode: ThemeMode;
        if (currentMode === 'system') {
          // If currently system, switch to opposite of current resolved theme
          newMode = currentResolved === 'dark' ? 'light' : 'dark';
        } else {
          // If currently light or dark, switch to the opposite
          newMode = currentMode === 'dark' ? 'light' : 'dark';
        }
        
        const newResolvedTheme = resolveTheme(newMode);
        applyThemeToDocument(newResolvedTheme);
        set({ mode: newMode, resolvedTheme: newResolvedTheme });
      },
      
      initializeTheme: () => {
        const currentMode = get().mode;
        const resolvedTheme = resolveTheme(currentMode);

        // Check if there's an initial theme set by the HTML script
        const initialTheme = (window as any).__INITIAL_THEME__;
        if (initialTheme && initialTheme === resolvedTheme) {
          // Theme is already applied, just update state
          set({ resolvedTheme });
        } else {
          // Apply theme and update state
          applyThemeToDocument(resolvedTheme);
          set({ resolvedTheme });
        }
      },
    }),
    {
      name: 'tcf-theme-mode',
      partialize: (state) => ({ mode: state.mode }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Re-resolve theme after hydration
          const resolvedTheme = resolveTheme(state.mode);
          applyThemeToDocument(resolvedTheme);
          state.resolvedTheme = resolvedTheme;
        }
      },
    }
  )
);

// System theme change listener
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  mediaQuery.addEventListener('change', () => {
    const state = useThemeStore.getState();
    if (state.mode === 'system') {
      const newResolvedTheme = getSystemTheme();
      applyThemeToDocument(newResolvedTheme);
      useThemeStore.setState({ resolvedTheme: newResolvedTheme });
    }
  });
}

// Hook to get theme-aware colors
export const useThemeColors = () => {
  const { resolvedTheme } = useThemeStore();
  
  const colors = {
    light: {
      // Background colors
      background: '#f7f9fb',
      surface: '#ffffff',
      surfaceHover: '#f8f9fa',
      
      // Text colors
      textPrimary: '#212529',
      textSecondary: '#6c757d',
      textDimmed: '#adb5bd',
      
      // Border colors
      border: '#eaeaea',
      borderLight: '#f1f3f4',
      
      // Brand colors (consistent across themes)
      primary: '#007bff',
      primaryHover: '#0056b3',
      
      // Section colors
      listening: '#007bff',
      reading: '#28a745',
      writing: '#fd7e14',
      speaking: '#dc3545',
    },
    dark: {
      // Background colors
      background: '#000000',
      surface: '#1a1a1a',
      surfaceHover: '#2d2d2d',
      
      // Text colors (as per user preference)
      textPrimary: '#f8f9fa',
      textSecondary: '#adb5bd',
      textDimmed: '#6c757d',
      
      // Border colors
      border: '#404040',
      borderLight: '#2d2d2d',
      
      // Brand colors (consistent across themes)
      primary: '#007bff',
      primaryHover: '#0056b3',
      
      // Section colors (slightly adjusted for dark mode)
      listening: '#1e90ff',
      reading: '#32cd32',
      writing: '#ff8c00',
      speaking: '#ff6b6b',
    },
  };
  
  return colors[resolvedTheme];
};
