import { Container, Title, SimpleGrid, Card, Text, Button, Group, Loader, Alert, Stack, Box, Divider } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { IconEar, IconBook, IconPencil, IconMicrophone, IconInfoCircle, IconBulb, IconDatabase, IconVolume, IconCheck, IconSettings, IconTargetArrow } from '@tabler/icons-react';
import { testApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next'
import { useContactSupport } from '../hooks/useContactSupport';
import { ContactSupportModal } from '../components/ContactSupportModal';
import { useThemeColors } from '../store/useThemeStore';
import { SEOHead, SEOConfigs } from '../components/SEOHead';

const sectionIcons = {
  listening: IconEar,
  reading: IconB<PERSON>,
  writing: IconPencil,
  speaking: IconMicrophone,
};

const sectionColors = {
  listening: '#007bff',
  reading: '#28a745',
  writing: '#fd7e14',
  speaking: '#dc3545',
};

const sectionButtonColors = {
  listening: 'blue',
  reading: 'green',
  writing: 'orange',
  speaking: 'red',
};

export function Home() {
  const { isAuthenticated } = useAuthStore();
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const {
    contactOpened,
    openContact,
    closeContact,
    contactForm,
    isSubmittingSupport,
    handleContactSupport
  } = useContactSupport();
  const { isLoading, error } = useQuery({
    queryKey: ['tests'],
    queryFn: testApi.getTests,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const sections = [
    {
      id: 'listening' as const,
      title: t('home.sections.listening.title'),
      description: t('home.sections.listening.description'),
      available: true,
      link: '/listening',
    },
    {
      id: 'reading' as const,
      title: t('home.sections.reading.title'),
      description: t('home.sections.reading.description'),
      available: true,
      link: '/reading',
    },
    {
      id: 'writing' as const,
      title: t('home.sections.writing.title'),
      description: t('home.sections.writing.description'),
      available: true,
      link: '/writing',
    },
    {
      id: 'speaking' as const,
      title: t('home.sections.speaking.title'),
      description: t('home.sections.speaking.description'),
      available: true,
      link: '/speaking',
    },
  ];

  // Updated features to highlight the 6 key advantages
  const features = [
    {
      icon: IconDatabase,
      title: t('home.features.comprehensive.title'),
      description: t('home.features.comprehensive.description'),
      color: '#228be6',
      highlight: true
    },
    {
      icon: IconVolume,
      title: t('home.features.enhanced.title'),
      description: t('home.features.enhanced.description'),
      color: '#40c057',
      highlight: true
    },
    {
      icon: IconCheck,
      title: t('home.features.verified.title'),
      description: t('home.features.verified.description'),
      color: '#fd7e14',
      highlight: true
    },
    {
      icon: IconBulb,
      title: t('home.features.explanations.title'),
      description: t('home.features.explanations.description'),
      color: '#e64980',
      highlight: true
    },
    {
      icon: IconSettings,
      title: t('home.features.customizable.title'),
      description: t('home.features.customizable.description'),
      color: '#7c3aed',
      highlight: true
    },
    {
      icon: IconTargetArrow,
      title: t('home.features.mockExams.title'),
      description: t('home.features.mockExams.description'),
      color: '#0ea5e9',
      highlight: true
    }
  ];

  if (isLoading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Loader size="xl" color="blue" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.tests')}</Text>
          <Text size="sm" c="dimmed">{t('common.pleaseWait')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert
          variant="light"
          color="red"
          title={t('home.errorTitle')}
          icon={<IconInfoCircle />}
        >
          {t('home.errorMessage')}
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <SEOHead {...SEOConfigs.home} />
      <Container size="lg" py="xl">
      {/* Hero Section */}
      <Box mb={60}>
        <Stack align="center" mb="xl" gap="md">
          <Box
            style={{
              background: 'linear-gradient(135deg, rgba(34, 139, 230, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%)',
              borderRadius: '24px',
              padding: '32px 48px',
              border: '1px solid rgba(34, 139, 230, 0.2)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 8px 32px rgba(34, 139, 230, 0.15)',
              maxWidth: '800px',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {/* Decorative elements */}
            <Box
              style={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 100,
                height: 100,
                background: 'linear-gradient(45deg, rgba(34, 139, 230, 0.1), rgba(124, 58, 237, 0.1))',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}
            />
            <Box
              style={{
                position: 'absolute',
                bottom: -30,
                left: -30,
                width: 80,
                height: 80,
                background: 'linear-gradient(45deg, rgba(124, 58, 237, 0.1), rgba(34, 139, 230, 0.1))',
                borderRadius: '50%',
                filter: 'blur(15px)'
              }}
            />

            <Stack gap="xs" align="center" style={{ position: 'relative', zIndex: 1 }}>
              <Text
                size="xl"
                fw={600}
                ta="center"
                style={{
                  background: 'linear-gradient(135deg, #228be6 0%, #7c3aed 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  lineHeight: 1.3,
                  fontFamily: 'system-ui, -apple-system, "Segoe UI", sans-serif'
                }}
              >
                🏠 Bienvenue Chez TCFCA
              </Text>
              <Text
                size="lg"
                fw={500}
                ta="center"
                c="dimmed"
                style={{
                  lineHeight: 1.4,
                  fontStyle: 'italic',
                  maxWidth: '600px'
                }}
              >
                Ici, c'est comme à la maison pour tous ceux qui préparent le TCF Canada
              </Text>
            </Stack>
          </Box>
        </Stack>
      </Box>

      {/* Maintenance Notification - Keep for other important notices */}
      <Box mb={24}>
        <Card
          shadow="lg"
          padding="md"
          radius="md"
          withBorder
          style={{
            background: 'light-dark(#fff1f0, #2a1216)',
            border: '3px solid light-dark(#ff4d4f, #ff7875)',
            maxWidth: 600,
            margin: '0 auto',
            fontSize: 15,
            color: 'light-dark(#a8071a, #ff9c9c)',
            lineHeight: 1.5,
            boxShadow: '0 4px 20px light-dark(rgba(255, 77, 79, 0.3), rgba(255, 120, 117, 0.5))',
          }}
        >
          {/* Translation Service Update */}
          <Box mb={4}>
            <Text fw={600} size="sm" mb={3} c="blue.7">
              🔄 <b>TRANSLATION SERVICE UPDATE / 翻译服务更新</b>
            </Text>

            <Box mb={3}>
              <Text size="sm" fw={500} mb={2}>
                <b>🌐 Default Translation Service / 默认翻译服务:</b>
              </Text>
              <ul style={{ margin: '4px 0 4px 20px', padding: 0, fontSize: '14px' }}>
                <li><b>English:</b> We now use Azure as the default translation service for better accessibility. GPT-4 translation is still available but users in restricted areas might not be able to access it. You can switch between Azure and GPT-4 in your profile page.</li>
                <li><b>中文：</b>我们现在使用Azure作为默认翻译服务以提高可访问性。GPT-4翻译仍然可用，但受限地区的用户可能无法访问。您可以在个人资料页面在Azure和GPT-4之间切换。</li>
              </ul>
            </Box>

            <Box mb={3}>
              <Text size="sm" fw={500} mb={2}>
                <b>⚠️ Regional Restrictions / 地区限制:</b>
              </Text>
              <ul style={{ margin: '4px 0 4px 20px', padding: 0, fontSize: '14px' }}>
                <li><b>English:</b> Users in certain restricted areas may not be able to use GPT-4 translator due to regional access limitations. Azure translator works globally and is set as the default option.</li>
                <li><b>中文：</b>某些受限地区的用户可能由于地区访问限制而无法使用GPT-4翻译器。Azure翻译器在全球范围内可用，已设为默认选项。</li>
              </ul>
            </Box>
          </Box>

          {/* Support & Contact Section */}
          <Box mb={4}>
            <Text fw={600} size="sm" mb={3} c="orange.7">
              ⚠️ <b>SUPPORT & CONTACT / 支持与联系</b>
            </Text>

            <Box mb={3}>
              <Text size="sm" fw={500} mb={2}>
                <b>📧 Email Issues / 邮箱问题:</b>
              </Text>
              <ul style={{ margin: '4px 0 4px 20px', padding: 0, fontSize: '14px' }}>
                <li><b>Hotmail/Outlook users:</b> If you have trouble receiving verification codes, please contact support or use a different email address.</li>
                <li><b>Hotmail/Outlook用户注意：</b>如果您无法收到验证码，请联系客服或使用其他邮箱。</li>
              </ul>
            </Box>
          </Box>
        </Card>
      </Box>

      {/* Key Features Section - Redesigned to highlight the 6 advantages */}
      <Box mb={60}>
        <Stack align="center" mb="xl" gap="lg">
          <Title order={2} size="2.5rem" fw={600} ta="center" c="dark">
            {t('home.whyChooseUs.title')}
          </Title>
        </Stack>
        
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="xl" mb="xl">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card
                key={index}
                shadow="md"
                padding="xl"
                radius="lg"
                withBorder
                style={{
                  height: '100%',
                  background: feature.highlight 
                    ? `linear-gradient(135deg, ${feature.color}08 0%, ${feature.color}03 100%)`
                    : 'white',
                  border: feature.highlight 
                    ? `2px solid ${feature.color}30`
                    : '1px solid #e9ecef',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-8px)';
                  e.currentTarget.style.boxShadow = `0 20px 40px ${feature.color}20`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
                }}
              >
                {feature.highlight && (
                  <Box
                    style={{
                      position: 'absolute',
                      top: -20,
                      right: -20,
                      width: 80,
                      height: 80,
                      background: `linear-gradient(45deg, ${feature.color}15, ${feature.color}05)`,
                      borderRadius: '50%',
                    }}
                  />
                )}
                <Stack gap="lg" align="center" style={{ textAlign: 'center', height: '100%' }}>
                  <Box
                    style={{
                      background: `linear-gradient(135deg, ${feature.color} 0%, ${feature.color}dd 100%)`,
                      borderRadius: '50%',
                      padding: '20px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: `0 8px 25px ${feature.color}30`
                    }}
                  >
                    <Icon size={36} color="white" />
                  </Box>
                  <Stack gap="sm" align="center">
                    <Title order={4} fw={600} c="dark" size="lg">
                      {feature.title}
                    </Title>
                  </Stack>
                  <Text size="sm" c="dimmed" style={{ flex: 1, lineHeight: 1.6 }}>
                    {feature.description}
                  </Text>
                </Stack>
              </Card>
            );
          })}
        </SimpleGrid>
      </Box>

      {/* Announcements Section - Modern Design */}
      <Box mb={60}>
        <Stack align="center" mb="xl" gap="md">
          <Title order={2} size="2rem" fw={600} ta="center" c="dark">
            {t('home.announcements.title')}
          </Title>
          <Text size="lg" c="dimmed" ta="center" maw={600}>
            {t('home.announcements.description')}
          </Text>
        </Stack>
        
        <SimpleGrid cols={{ base: 1, md: 3 }} spacing="xl">
          {/* Promo Code Announcement */}
          <Card
            shadow="lg"
            padding="xl"
            radius="xl"
            withBorder
            style={{
              background: 'linear-gradient(135deg, rgba(34, 139, 230, 0.08) 0%, rgba(34, 139, 230, 0.03) 100%)',
              border: '2px solid rgba(34, 139, 230, 0.2)',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 25px 50px rgba(34, 139, 230, 0.25)';
              e.currentTarget.style.borderColor = 'rgba(34, 139, 230, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(34, 139, 230, 0.15)';
              e.currentTarget.style.borderColor = 'rgba(34, 139, 230, 0.2)';
            }}
          >
            {/* Decorative Elements */}
            <Box
              style={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 100,
                height: 100,
                background: 'linear-gradient(45deg, rgba(34, 139, 230, 0.15), rgba(34, 139, 230, 0.05))',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}
            />
            <Box
              style={{
                position: 'absolute',
                bottom: -30,
                left: -30,
                width: 80,
                height: 80,
                background: 'linear-gradient(45deg, rgba(34, 139, 230, 0.1), rgba(34, 139, 230, 0.03))',
                borderRadius: '50%',
                filter: 'blur(15px)'
              }}
            />
            
            <Stack gap="lg" style={{ height: '100%', position: 'relative', zIndex: 1 }}>
              {/* Icon Header */}
              <Box style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Box
                  style={{
                    background: 'linear-gradient(135deg, #228be6 0%, #1c7ed6 100%)',
                    borderRadius: '12px',
                    padding: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 8px 20px rgba(34, 139, 230, 0.3)'
                  }}
                >
                  <Text size="xl" style={{ filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))' }}>⚡</Text>
                </Box>
                <Text
                  fw={700}
                  size="xl"
                  style={{
                    background: 'linear-gradient(135deg, #228be6 0%, #1c7ed6 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}
                  dangerouslySetInnerHTML={{ __html: t('home.announcements.promoCode.title') }}
                />
              </Box>
              
              <Text
                size="sm"
                c="dimmed"
                style={{ lineHeight: 1.7, flex: 1, fontSize: '14px' }}
                dangerouslySetInnerHTML={{ __html: t('home.announcements.promoCode.message') }}
              />
              
              <Box style={{ marginTop: 'auto' }}>
                <Button
                  size="md"
                  style={{
                    background: 'linear-gradient(135deg, #228be6 0%, #1c7ed6 100%)',
                    border: 'none',
                    borderRadius: '12px',
                    width: '100%',
                    padding: '12px 24px',
                    fontWeight: 600,
                    fontSize: '14px',
                    boxShadow: '0 8px 20px rgba(34, 139, 230, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  component={Link}
                  to="/redeem-promo"
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 12px 30px rgba(34, 139, 230, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(34, 139, 230, 0.3)';
                  }}
                >
                  {t('home.announcements.promoCode.actionText')}
                </Button>
              </Box>
            </Stack>
          </Card>

          {/* File Download Card */}
          <Card
            shadow="lg"
            padding="xl"
            radius="xl"
            withBorder
            style={{
              background: 'linear-gradient(135deg, rgba(253, 126, 20, 0.08) 0%, rgba(253, 126, 20, 0.03) 100%)',
              border: '2px solid rgba(253, 126, 20, 0.2)',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 25px 50px rgba(253, 126, 20, 0.25)';
              e.currentTarget.style.borderColor = 'rgba(253, 126, 20, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(253, 126, 20, 0.15)';
              e.currentTarget.style.borderColor = 'rgba(253, 126, 20, 0.2)';
            }}
          >
            {/* Decorative Elements */}
            <Box
              style={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 100,
                height: 100,
                background: 'linear-gradient(45deg, rgba(253, 126, 20, 0.15), rgba(253, 126, 20, 0.05))',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}
            />
            <Box
              style={{
                position: 'absolute',
                bottom: -30,
                left: -30,
                width: 80,
                height: 80,
                background: 'linear-gradient(45deg, rgba(253, 126, 20, 0.1), rgba(253, 126, 20, 0.03))',
                borderRadius: '50%',
                filter: 'blur(15px)'
              }}
            />
            
            <Stack gap="lg" style={{ height: '100%', position: 'relative', zIndex: 1 }}>
              {/* Icon Header */}
              <Box style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Box
                  style={{
                    background: 'linear-gradient(135deg, #fd7e14 0%, #e8590c 100%)',
                    borderRadius: '12px',
                    padding: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 8px 20px rgba(253, 126, 20, 0.3)'
                  }}
                >
                  <Text size="xl" style={{ filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))' }}>📚</Text>
                </Box>
                <Text
                  fw={700}
                  size="xl"
                  style={{
                    background: 'linear-gradient(135deg, #fd7e14 0%, #e8590c 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}
                  dangerouslySetInnerHTML={{ __html: t('home.announcements.downloads.title') }}
                />
              </Box>
              
              <Text
                size="sm"
                c="dimmed"
                style={{ lineHeight: 1.7, flex: 1, fontSize: '14px' }}
                dangerouslySetInnerHTML={{ __html: t('home.announcements.downloads.message') }}
              />
              
              <Box style={{ marginTop: 'auto' }}>
                {isAuthenticated ? (
                  <Button
                    size="md"
                    style={{
                      background: 'linear-gradient(135deg, #fd7e14 0%, #e8590c 100%)',
                      border: 'none',
                      borderRadius: '12px',
                      width: '100%',
                      padding: '12px 24px',
                      fontWeight: 600,
                      fontSize: '14px',
                      boxShadow: '0 8px 20px rgba(253, 126, 20, 0.3)',
                      transition: 'all 0.3s ease'
                    }}
                    component={Link}
                    to="/downloads"
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 12px 30px rgba(253, 126, 20, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 8px 20px rgba(253, 126, 20, 0.3)';
                    }}
                  >
                    {t('home.announcements.downloads.actionText')}
                  </Button>
                ) : (
                  <Button
                    size="md"
                    style={{
                      background: 'linear-gradient(135deg, #fd7e14 0%, #e8590c 100%)',
                      border: 'none',
                      borderRadius: '12px',
                      width: '100%',
                      padding: '12px 24px',
                      fontWeight: 600,
                      fontSize: '14px',
                      boxShadow: '0 8px 20px rgba(253, 126, 20, 0.3)',
                      transition: 'all 0.3s ease'
                    }}
                    component={Link}
                    to="/login"
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 12px 30px rgba(253, 126, 20, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 8px 20px rgba(253, 126, 20, 0.3)';
                    }}
                  >
                    {t('home.announcements.downloads.loginText')}
                  </Button>
                )}
              </Box>
            </Stack>
          </Card>

          {/* Bug Report Announcement */}
          <Card
            shadow="lg"
            padding="xl"
            radius="xl"
            withBorder
            style={{
              background: 'linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(40, 167, 69, 0.03) 100%)',
              border: '2px solid rgba(40, 167, 69, 0.2)',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 25px 50px rgba(40, 167, 69, 0.25)';
              e.currentTarget.style.borderColor = 'rgba(40, 167, 69, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(40, 167, 69, 0.15)';
              e.currentTarget.style.borderColor = 'rgba(40, 167, 69, 0.2)';
            }}
          >
            {/* Decorative Elements */}
            <Box
              style={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 100,
                height: 100,
                background: 'linear-gradient(45deg, rgba(40, 167, 69, 0.15), rgba(40, 167, 69, 0.05))',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}
            />
            <Box
              style={{
                position: 'absolute',
                bottom: -30,
                left: -30,
                width: 80,
                height: 80,
                background: 'linear-gradient(45deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.03))',
                borderRadius: '50%',
                filter: 'blur(15px)'
              }}
            />
            
            <Stack gap="lg" style={{ height: '100%', position: 'relative', zIndex: 1 }}>
              {/* Icon Header */}
              <Box style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Box
                  style={{
                    background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                    borderRadius: '12px',
                    padding: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 8px 20px rgba(40, 167, 69, 0.3)'
                  }}
                >
                  <Text size="xl" style={{ filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))' }}>🐛</Text>
                </Box>
                <Text
                  fw={700}
                  size="xl"
                  style={{
                    background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}
                  dangerouslySetInnerHTML={{ __html: t('home.announcements.bugReport.title') }}
                />
              </Box>
              
              <Text
                size="sm"
                c="dimmed"
                style={{ lineHeight: 1.7, flex: 1, fontSize: '14px' }}
                dangerouslySetInnerHTML={{ __html: t('home.announcements.bugReport.message') }}
              />
              
              <Box style={{ marginTop: 'auto' }}>
                <Button
                  size="md"
                  style={{
                    background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                    border: 'none',
                    borderRadius: '12px',
                    width: '100%',
                    padding: '12px 24px',
                    fontWeight: 600,
                    fontSize: '14px',
                    boxShadow: '0 8px 20px rgba(40, 167, 69, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  onClick={openContact}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 12px 30px rgba(40, 167, 69, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(40, 167, 69, 0.3)';
                  }}
                >
                  {t('home.announcements.bugReport.actionText')}
                </Button>
              </Box>
            </Stack>
          </Card>
        </SimpleGrid>
      </Box>

      <Divider my="xl" />

      {/* Test Sections */}
      <Box mb={60}>
        <Stack align="center" mb="xl" gap="lg">
        </Stack>
        
        <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
        {sections.map((section) => {
          const Icon = sectionIcons[section.id];
          const color = sectionColors[section.id];
          const buttonColor = sectionButtonColors[section.id];

          return (
            <Card
              key={section.id}
              shadow="md"
              padding="xl"
              radius="lg"
              withBorder
              style={{
                height: '100%',
                background: themeColors.surface,
                borderColor: themeColors.border,
                color: themeColors.textPrimary,
                border: `2px solid ${color}20`,
                transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s ease, box-shadow 0.3s ease',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e: React.MouseEvent<HTMLDivElement>) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = `0 15px 35px ${color}25`;
              }}
              onMouseLeave={(e: React.MouseEvent<HTMLDivElement>) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
              }}
            >
              <Box
                style={{
                  position: 'absolute',
                  top: -30,
                  right: -30,
                  width: 100,
                  height: 100,
                  background: `linear-gradient(45deg, ${color}15, ${color}05)`,
                  borderRadius: '50%',
                }}
              />
              <Stack gap="lg" align="center" style={{ textAlign: 'center', height: '100%' }}>
                <Box
                  style={{
                    background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`,
                    borderRadius: '50%',
                    padding: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 8px 25px ${color}30`
                  }}
                >
                  <Icon size={48} color="white" />
                </Box>

                <div>
                  <Title order={3} fw={700} mb="xs" c={themeColors.textPrimary} size="xl">
                    {section.title}
                  </Title>
                  <Text size="md" c={themeColors.textSecondary} style={{ lineHeight: 1.6 }}>
                    {section.description}
                  </Text>
                </div>

                {section.available ? (
                  <Link
                    to={section.link || `/${section.id}`}
                    style={{ textDecoration: 'none', marginTop: 'auto' }}
                  >
                    <Button
                      color={buttonColor}
                      size="lg"
                      style={{ 
                        minWidth: '140px',
                        background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`,
                        border: 'none'
                      }}
                    >
                      {t('home.startButton')}
                    </Button>
                  </Link>
                ) : (
                  <Button
                    variant="outline"
                    color="gray"
                    size="lg"
                    disabled
                    style={{ minWidth: '140px', marginTop: 'auto' }}
                  >
                    {t('home.comingSoon')}
                  </Button>
                )}
              </Stack>
            </Card>
          );
        })}
      </SimpleGrid>
      </Box>

      {/* Call to Action for Non-authenticated Users */}
      {!isAuthenticated && (
        <Card
          mt="xl"
          padding="xl"
          radius="lg"
          withBorder
          style={{
            background: themeColors.surface,
            textAlign: 'center',
            border: `2px solid ${themeColors.primary}`,
            borderColor: themeColors.primary,
            transition: 'all 0.3s ease'
          }}
        >
          <Stack align="center" gap="lg">
            <Title order={2} size="2rem" fw={600} c={themeColors.textPrimary}>
              {t('home.ctaTitle')}
            </Title>
            <Text size="lg" c={themeColors.textSecondary} maw={600} style={{ lineHeight: 1.6 }}>
              {t('home.ctaSubtitle')}
            </Text>
            <Group justify="center" gap="lg">
              <Link to="/register" style={{ textDecoration: 'none' }}>
                <Button 
                  size="xl" 
                  style={{
                    background: 'linear-gradient(135deg, #228be6 0%, #7c3aed 100%)',
                    border: 'none',
                    padding: '16px 32px'
                  }}
                >
                  {t('home.registerButton')}
                </Button>
              </Link>
              <Link to="/login" style={{ textDecoration: 'none' }}>
                <Button 
                  variant="outline" 
                  size="xl" 
                  color="blue"
                  style={{
                    padding: '16px 32px',
                    borderWidth: '2px'
                  }}
                >
                  {t('home.loginButton')}
                </Button>
              </Link>
            </Group>
          </Stack>
        </Card>
      )}

      {/* Contact Support Modal */}
      <ContactSupportModal
        opened={contactOpened}
        onClose={closeContact}
        contactForm={contactForm}
        isSubmitting={isSubmittingSupport}
        onSubmit={handleContactSupport}
      />
    </Container>
    </>
  );
}