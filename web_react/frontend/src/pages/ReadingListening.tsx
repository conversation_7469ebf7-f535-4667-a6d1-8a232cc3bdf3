import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Container,
  Title,
  Paper,
  Stack,
  Radio,
  Group,
  Button,
  Text,
  Box,
  Alert,
  Loader,
  Card,
  Flex,
  Modal,
  Divider,
  Badge,
  Center,
  ActionIcon,
  Textarea,
  Image,
  Select,
  Checkbox,
  Tooltip,
  ScrollArea,
  Collapse,
  Notification
} from '@mantine/core';
import { IconArrowLeft, IconDeviceFloppy, IconEye, IconEyeOff, IconCheck, IconX, IconAlertCircle, IconChevronLeft, IconNotebook, IconChevronRight, IconPlayerPlay, IconPlayerPause, IconPlaylistAdd, IconNote, IconArrowRight, IconClock, IconStar, IconStarFilled, IconVolume, IconVolumeOff, IconBrain, IconLanguage } from '@tabler/icons-react';
import { testApi, assetsApi, translationApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { useTestStore } from '../store/useTestStore';
import { useHighlights } from '../hooks/useHighlights';
import { UnifiedTextInteraction } from '../components/UnifiedTextInteraction';
import { BookmarkIcon } from '../components/BookmarkIcon';
import { notifications } from '@mantine/notifications';
import { NotebookSidebar } from '../components/NotebookSidebar';
import { AnalysisSidebar } from '../components/AnalysisSidebar';
import { FeatureHint } from '../components/FeatureHint';
import { useTranslation } from 'react-i18next';
import { useAudioSettings } from '../contexts/AudioSettingsContext';
import { CustomAudioPlayer } from '../components/CustomAudioPlayer';
import type { CustomAudioPlayerRef } from '../components/CustomAudioPlayer';
import { GradingResultsModal } from '../components/GradingResultsModal';
import { useThemeColors, useThemeStore } from '../store/useThemeStore';
import { useAudio, useImage } from '../hooks/useMedia';
import { OptimizedImage } from '../components/OptimizedImage';

// Define the actual question structure from the API
interface ApiQuestion {
  extracted_text?: string;
  question_text?: string;
  choices?: Record<string, string>;
  image_path?: string;
  audio_path?: string;
  chunks?: Array<{
    start: number;
    end: number;
    text: string;
  }>;
  locations?: string[];
}

// Navigation groups for TCF levels
const navGroups = [
  { label: 'A1', colorClass: 'a1', points: 3, range: [1, 4], color: '#28a745' },
  { label: 'A2', colorClass: 'a2', points: 9, range: [5, 10], color: '#28a745' },
  { label: 'B1', colorClass: 'b1', points: 15, range: [11, 19], color: '#ff9800' },
  { label: 'B2', colorClass: 'b2', points: 21, range: [20, 29], color: '#ff9800' },
  { label: 'C1', colorClass: 'c1', points: 26, range: [30, 35], color: '#e53935' },
  { label: 'C2', colorClass: 'c2', points: 33, range: [36, 39], color: '#e53935' },
];

// Helper function to clean up test ID display
const formatTestId = (testId?: string): string => {
  if (!testId) return '';
  // Remove "test" prefix if it exists, keeping only the number
  const match = testId.match(/^test(\d+)$/i);
  if (match) {
    return match[1]; // Return just the number
  }
  return testId;
};

export function ReadingListening() {
  const { testId, groupId } = useParams<{ testId?: string; groupId?: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const audioRef = useRef<CustomAudioPlayerRef>(null);
  const { user } = useAuthStore();
  const { answers, setAnswer, clearCurrentTest } = useTestStore();
  const { t, i18n } = useTranslation();
  const { settings, toggleAutoPlay, setPlaybackSpeed } = useAudioSettings();
  const themeColors = useThemeColors();
  const { resolvedTheme } = useThemeStore();

  // Extract section from the current path (e.g., /reading/test12 -> reading)
  const section = location.pathname.split('/')[1]; // Gets the first path segment

  const isFree = searchParams.get('free') === '1';
  const isGroupTest = location.pathname.includes('/group/'); // Detect group tests by URL pattern
  const isMockExam = location.pathname.includes('/mock/'); // Detect mock exams by URL pattern
  const testIdentifier = isGroupTest ? `group${groupId}` : testId;

  const [currentQuestion, setCurrentQuestion] = useState<number | null>(null); // Start with null to prevent flash
  const [wrongQuestions, setWrongQuestions] = useState<number[]>([]);
  const [isQuestionInitialized, setIsQuestionInitialized] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);
  const [transcriptVisible, setTranscriptVisible] = useState<{ [key: number]: boolean }>({});
  const [showAllTranscript, setShowAllTranscript] = useState(false);
  const [activeChunk, setActiveChunk] = useState<number | null>(null);
  const currentChunkListenerRef = useRef<(() => void) | null>(null);

  // Get current language from i18n
  const currentLanguage = i18n.language;

  // Set default translation target based on current UI language
  const getDefaultTranslationTarget = (): 'en' | 'zh' => {
    if (currentLanguage.startsWith('zh')) {
      return 'zh'; // Chinese UI -> translate to Chinese
    } else {
      return 'en'; // English/French UI -> translate to English
    }
  };

  // Robust text display system - prioritizes OCR format while preventing wide text
  const prepareTextForDisplay = (text: string): {
    displayText: string;
    useFlexibleWrapping: boolean;
    hasOCRFormat: boolean;
  } => {
    if (!text) return { displayText: '', useFlexibleWrapping: false, hasOCRFormat: false };

    // Clean the text first - remove OCR artifacts but preserve structure
    const cleanedText = text
      .replace(/^```[\r\n]?/, '')   // Remove leading backticks
      .replace(/[\r\n]?```$/, '')   // Remove trailing backticks
      .replace(/^- Text:\s*```\s*/gm, '') // Remove "- Text:" prefixes
      .replace(/^Sure! Here.*?```\s*/gm, '') // Remove OCR artifacts
      .trim();

    // Analyze the text structure to determine if it has good OCR formatting
    const lines = cleanedText.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);

    // Check if text has good OCR structure (multiple lines with reasonable lengths)
    const hasMultipleLines = nonEmptyLines.length > 1;
    const hasReasonableLineLengths = nonEmptyLines.every(line => line.trim().length <= 120);
    const hasVariedLineLengths = nonEmptyLines.some(line => line.trim().length < 80) &&
                                 nonEmptyLines.some(line => line.trim().length > 20);

    // Determine if we have good OCR format
    const hasOCRFormat = hasMultipleLines && hasReasonableLineLengths && hasVariedLineLengths;

    // Check for problematic cases that need flexible wrapping
    const hasVeryLongLines = lines.some(line => line.trim().length > 120);
    const hasSingleLongLine = lines.length === 1 && lines[0].length > 100;
    const hasExcessivelyLongLines = lines.some(line => line.trim().length > 200);

    // Decision logic:
    // 1. If we have good OCR format and no problematic lines -> use OCR format
    // 2. If we have problematic lines -> use flexible wrapping to prevent wide text
    const useFlexibleWrapping = hasVeryLongLines || hasSingleLongLine || hasExcessivelyLongLines;

    return {
      displayText: cleanedText,
      useFlexibleWrapping,
      hasOCRFormat: hasOCRFormat && !useFlexibleWrapping
    };
  };

  // Translation state for listening
  const [selectedTranscriptText, setSelectedTranscriptText] = useState('');
  const [showTranslateButton, setShowTranslateButton] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationTarget, setTranslationTarget] = useState<'en' | 'zh'>(getDefaultTranslationTarget());
  const [translationResult, setTranslationResult] = useState<{
    text: string;
    language: string;
  } | null>(null);
  
  // Notebook state - Start collapsed for clean test experience
  const [notebookExpanded, setNotebookExpanded] = useState({
    rightExpanded: false,
    leftExpanded: false
  });
  
  // Analysis sidebar state - Start collapsed for clean test experience
  const [analysisExpanded, setAnalysisExpanded] = useState(false);
  
  // Add modal state
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [gradingResults, setGradingResults] = useState<any>(null);

  // Feature hint state
  const [showFeatureHint, setShowFeatureHint] = useState(false);

  // Bookmark status tracking for navigation panel
  const [bookmarkStatuses, setBookmarkStatuses] = useState<Record<number, boolean>>({});
  const [isLoadingBookmarks, setIsLoadingBookmarks] = useState(false);

  // Determine if this is a listening test
  const isListeningTest = section === 'listening';

  // Toggle notebook
  const toggleNotebook = () => {
    const newExpanded = {
      ...notebookExpanded,
      rightExpanded: !notebookExpanded.rightExpanded
    };
    setNotebookExpanded(newExpanded);
    localStorage.setItem('tcf-notebook-expanded', newExpanded.rightExpanded.toString());
  };

  // Toggle analysis sidebar
  const toggleAnalysis = () => {
    const newExpanded = {
      ...notebookExpanded,
      leftExpanded: !notebookExpanded.leftExpanded
    };
    setNotebookExpanded(newExpanded);
    localStorage.setItem('tcf-analysis-expanded', newExpanded.leftExpanded.toString());
  };

  // Enable highlighting for ALL reading tests (individual AND group tests)
  const shouldShowHighlighting = !isListeningTest;
  
  // Initialize highlighting hook only when needed - use hooks conditionally is not recommended, so always call it
  const highlightHookResult = useHighlights({
    section: section!,
    testId: testIdentifier!,
    questionIndex: currentQuestion ?? 0,
    isFree,
  });
  
  // Extract highlighting functions with conditional usage
  const highlights = shouldShowHighlighting ? highlightHookResult.highlights : [];
  const addHighlight = shouldShowHighlighting ? highlightHookResult.addHighlight : (() => {});
  const removeHighlight = shouldShowHighlighting ? highlightHookResult.removeHighlight : (() => {});

  // ALL HOOKS MUST BE CALLED BEFORE ANY EARLY RETURNS
  const { data: testData, isLoading, error } = useQuery({
    queryKey: ['test', section, testIdentifier, isFree, isGroupTest],
    queryFn: () => {
      if (isGroupTest) {
        return testApi.getGroupTestData(section!, parseInt(groupId!));
      } else {
        return testApi.getTestData(section!, testId!, isFree);
      }
    },
    enabled: !!section && (!!testIdentifier),
  });

  // Load test info with previous answers and current question position
  const { data: testInfo, isLoading: isLoadingInfo } = useQuery({
    queryKey: ['test-info', section, testIdentifier, isFree, isGroupTest],
    queryFn: async () => {
      if (isGroupTest) {
        return await testApi.getGroupTestInfo(section!, parseInt(groupId!));
      } else {
        return await testApi.getTestInfo(section!, testId!, isFree);
      }
    },
    enabled: !!section && (!!testIdentifier),
  });

  // Initialize answers and current question from saved data
  useEffect(() => {
    if (testInfo) {
    // Clear the store first to ensure no contamination from previous tests
    clearCurrentTest();
    
      if (testInfo.previous_answers) {
      // Load previous answers into the store
      Object.entries(testInfo.previous_answers).forEach(([questionId, answer]) => {
        if (typeof answer === 'string' || Array.isArray(answer)) {
          setAnswer(questionId, answer);
        }
      });
    }
    
    // Load grading results if available (for previously graded tests)
      if (testInfo.grading_results) {
      const wrongQuestionNumbers = testInfo.grading_results.incorrect || [];
      setWrongQuestions(wrongQuestionNumbers);
      
      // Show notification to user about graded test
      notifications.show({
          title: t('test.graded.title'),
          message: `${testInfo.grading_results.correct?.length || 0} ${t('test.graded.correct')} ${wrongQuestionNumbers.length} ${t('test.graded.incorrect')}`,
        color: 'green',
        autoClose: 5000,
      });
    }
    
    // Set current question from saved position
      if (testInfo.current_question !== undefined && testInfo.current_question >= 0 && testData) {
      // Get the questions array to check bounds
      const questions = isGroupTest 
        ? (testData as any)?.group_data || []
        : isMockExam
        ? (testData as any)?.questions || []
        : testData as unknown as ApiQuestion[];
      
      // Only proceed if we have questions loaded
      if (questions.length > 0) {
        // Bounds check: ensure current_question is within valid range
        const maxQuestionIndex = questions.length - 1;
        const validCurrentQuestion = Math.min(Math.max(0, testInfo.current_question), maxQuestionIndex);
        
        if (testInfo.current_question !== validCurrentQuestion) {
            // Position was out of bounds and has been corrected
        }

        setCurrentQuestion(validCurrentQuestion);
        setIsQuestionInitialized(true);
        
        // Show notification to user about resuming
        if (validCurrentQuestion > 0 && !testInfo.grading_results) {
          // Only show resume notification if test is not already graded
          notifications.show({
              title: t('test.resumed.title'),
              message: `${t('test.resumed.message')} ${validCurrentQuestion + 1}`,
            color: 'blue',
          });
        }
        } else {
          // No questions available, but still mark as initialized
          setCurrentQuestion(0);
          setIsQuestionInitialized(true);
        }
      } else {
        // No saved position, start from beginning
        setCurrentQuestion(0);
        setIsQuestionInitialized(true);
      }
    } else if (testData) {
      // testInfo is loaded but no data, mark as initialized
      setCurrentQuestion(0);
      setIsQuestionInitialized(true);
    }
  }, [testInfo, setAnswer, clearCurrentTest, isGroupTest, section, testIdentifier, isFree, testData, t]);

  // Auto-save current question position when it changes
  useEffect(() => {
    if (testInfo?.has_history && currentQuestion !== null && currentQuestion >= 0) {
      // Only save if we have an established test session
      const saveCurrentPosition = async () => {
        try {
          await testApi.saveTestHistory({
            section: section!,
            test_id: testIdentifier!,
            free: isFree,
            answers: answers,
            current_question: currentQuestion ?? undefined,
          });
        } catch (error) {
          console.error('Failed to save current position:', error);
        }
      };
      
      const timeoutId = setTimeout(saveCurrentPosition, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [currentQuestion, testInfo, section, testIdentifier, isFree, answers, t]);

  // Show feature hint when user first enters the test
  useEffect(() => {
    if (isQuestionInitialized && testData && !isLoading && !isLoadingInfo) {
      // Check if user has seen the hint before (localStorage key per section)
      const hintKey = `tcf-feature-hint-${section}`;
      const hasSeenHint = localStorage.getItem(hintKey);

      if (!hasSeenHint) {
        // Show hint after a short delay to let the page settle
        const timer = setTimeout(() => {
          setShowFeatureHint(true);
          localStorage.setItem(hintKey, 'true');
        }, 1500);

        // Hide hint after 5 seconds
        const hideTimer = setTimeout(() => {
          setShowFeatureHint(false);
        }, 6500);

        return () => {
          clearTimeout(timer);
          clearTimeout(hideTimer);
        };
      }
    }
  }, [isQuestionInitialized, testData, isLoading, isLoadingInfo, section]);

  // Reset expanded states and clear translation when question changes
  useEffect(() => {
    setTranscriptVisible({});
    setShowAllTranscript(false);

    // Clear listening translation state when switching questions
    if (isListeningTest) {
      setSelectedTranscriptText('');
      setShowTranslateButton(false);
      setTranslationResult(null);
      // Clear any text selection
      window.getSelection()?.removeAllRanges();
    }
  }, [currentQuestion, isListeningTest]);

  const submitMutation = useMutation({
    mutationFn: async (data: any) => {
      // First calculate score
      const scoreResult = await testApi.calculateScore({
        section: section!,
        test_id: testIdentifier!,
        answers: answers,
        free: isFree,
      });
      
      // Generate grading results from the score calculation
      const gradingResults = {
        correct: [] as number[],
        incorrect: [] as number[],
        wrong_details: scoreResult.wrong_details || []
      };
      
      // Determine correct and incorrect questions
      const totalQuestions = (testData as any)?.group_data?.length || (testData as unknown as ApiQuestion[])?.length || 39;
      const wrongQuestionNumbers = scoreResult.wrong_details?.map((detail: any) => parseInt(detail.question)) || [];
      
      for (let i = 1; i <= totalQuestions; i++) {
        if (answers[i.toString()]) { // Only include questions that were answered
          if (wrongQuestionNumbers.includes(i)) {
            gradingResults.incorrect.push(i);
          } else {
            gradingResults.correct.push(i);
          }
        }
      }
      
      // Then save test history with final completion and grading results
      await testApi.saveTestHistory({
        section: section!,
        test_id: testIdentifier!,
        free: isFree,
        answers: answers,
        current_question: currentQuestion ?? undefined, // Save final position
        score: scoreResult.score,
        max_score: scoreResult.max_score,
        grading_results: gradingResults, // Save grading results
      });
      
      return { ...scoreResult, totalQuestions };
    },
    onSuccess: (data: any) => {
      if (data?.wrong_details) {
        setWrongQuestions(data.wrong_details.map((q: any) => parseInt(q.question)));
      }
      
      // Format results for the modal
      const modalResults = {
        score: data.score,
        max_score: data.max_score || data.totalQuestions,
        score_699: data.score_699,
        percent_699: data.percent_699,
        correct_count: data.correct_count,
        wrong_details: data.wrong_details || []
      };
      
      // Create question ID mapping for collection
      const questionIdMapping: Record<string, string> = {};
      if (data.wrong_details) {
        data.wrong_details.forEach((detail: any) => {
          const questionNumber = detail.question;
          const questionIndex = parseInt(questionNumber) - 1;
          // Get the actual question data for this question number
          const question = questions?.[questionIndex];
          // Always use the real database question_id (UUID) if present
          const realQuestionId = question?.question_id;
          if (realQuestionId) {
            questionIdMapping[questionNumber] = realQuestionId;
          }
        });
      }
      
      setGradingResults({
        ...modalResults,
        questionIds: questionIdMapping,
        testType: section as 'reading' | 'listening',
        testId: testIdentifier!,
        isGroupTest: isGroupTest
      });
      setShowResultsModal(true);
    },
    onError: (error: any) => {
      console.error('Failed to submit test:', error);
      alert(t('test.submitted.error'));
    },
  });

  const saveMutation = useMutation({
    mutationFn: () =>
      testApi.saveTestHistory({
        section: section!,
        test_id: testIdentifier!,
        free: isFree,
        answers: answers,
        current_question: currentQuestion ?? undefined, // Include current question position
      }),
  });

  // Timer effect - define handleSubmit first
  const handleSubmit = () => {
    if (audioRef.current?.audioElement) {
      audioRef.current.audioElement.pause();
    }
    submitMutation.mutate({});
  };

  const handleAnswerChange = (questionId: string, value: string) => {
    setAnswer(questionId, value);
  };

  const handleSave = () => {
    saveMutation.mutate();
  };

  const goToQuestion = (index: number) => {
    setCurrentQuestion(index);
    setShowTranscript(false);
    setActiveChunk(null);

    // Clear listening translation state when manually navigating
    if (isListeningTest) {
      setSelectedTranscriptText('');
      setShowTranslateButton(false);
      setTranslationResult(null);
      window.getSelection()?.removeAllRanges();
    }
  };

  const playChunk = (chunk: { start: number; end: number; text: string }) => {
    if (!audioRef.current?.audioElement) return;

    // Remove any existing chunk listener to prevent conflicts
    if (currentChunkListenerRef.current) {
      audioRef.current.audioElement.removeEventListener('timeupdate', currentChunkListenerRef.current);
      currentChunkListenerRef.current = null;
    }

    setActiveChunk(chunk.start);
    audioRef.current.audioElement.currentTime = chunk.start;
    audioRef.current.audioElement.play();

    const onTimeUpdate = () => {
      if (audioRef.current?.audioElement && audioRef.current.audioElement.currentTime >= chunk.end + 0.5) {
        audioRef.current.pause(); // Use the CustomAudioPlayer's pause method to update state
        audioRef.current.audioElement.removeEventListener('timeupdate', onTimeUpdate);
        currentChunkListenerRef.current = null;
        setActiveChunk(null);
      }
    };

    // Store the listener reference and add it
    currentChunkListenerRef.current = onTimeUpdate;
    audioRef.current.audioElement.addEventListener('timeupdate', onTimeUpdate);
  };

  const toggleTranscriptChunk = (index: number) => {
    setTranscriptVisible(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const toggleAllTranscript = () => {
    const newShowAll = !showAllTranscript;
    setShowAllTranscript(newShowAll);

    if (currentQ?.chunks) {
      const newVisible: { [key: number]: boolean } = {};
      currentQ.chunks?.forEach((_: any, index: number) => {
        newVisible[index] = newShowAll;
      });
      setTranscriptVisible(newVisible);
    }
  };

  // Handle text selection in transcript
  const handleTranscriptTextSelection = () => {
    const selection = window.getSelection();

    if (selection && selection.rangeCount > 0 && selection.toString().trim()) {
      const selectedText = selection.toString().trim();
      const range = selection.getRangeAt(0);

      // Check if selection is within transcript container or any transcript card
      const transcriptContainer = document.querySelector('.transcript-container');
      const transcriptCards = document.querySelectorAll('.transcript-card');

      let isWithinTranscript = false;

      // Check transcript container
      if (transcriptContainer) {
        isWithinTranscript = transcriptContainer.contains(range.commonAncestorContainer) ||
                           transcriptContainer.contains(range.startContainer) ||
                           transcriptContainer.contains(range.endContainer);
      }

      // Also check individual transcript cards
      if (!isWithinTranscript) {
        transcriptCards.forEach(card => {
          if (card.contains(range.commonAncestorContainer) ||
              card.contains(range.startContainer) ||
              card.contains(range.endContainer)) {
            isWithinTranscript = true;
          }
        });
      }

      if (isWithinTranscript) {
        setSelectedTranscriptText(selectedText);
        setShowTranslateButton(true);
      } else {
        setShowTranslateButton(false);
      }
    } else {
      setShowTranslateButton(false);
    }
  };

  // Translation function
  const handleTranslateText = async () => {
    if (!selectedTranscriptText.trim()) return;

    setIsTranslating(true);
    try {
      const result = await translationApi.translate(selectedTranscriptText, translationTarget, 'fr');

      if (result.success && result.translated_text) {
        setTranslationResult({
          text: result.translated_text,
          language: translationTarget === 'en' ? 'English' : '中文'
        });

        notifications.show({
          title: 'Translation Complete',
          message: `Translated to ${translationTarget === 'en' ? 'English' : 'Chinese'}`,
          color: 'green',
        });
      } else {
        notifications.show({
          title: 'Translation Failed',
          message: result.error || 'Unknown error occurred',
          color: 'red',
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Translation Error',
        message: 'Failed to translate text. Please try again.',
        color: 'red',
      });
    } finally {
      setIsTranslating(false);
    }
  };

  // Toggle translation target language
  const toggleTranslationTarget = () => {
    setTranslationTarget(prev => prev === 'en' ? 'zh' : 'en');
    setTranslationResult(null); // Clear previous result when switching
  };

  // Update translation target when language changes
  useEffect(() => {
    const newTarget = getDefaultTranslationTarget();
    setTranslationTarget(newTarget);
    // Clear previous translation when language changes
    setTranslationResult(null);
  }, [currentLanguage]);

  // Add event listeners for text selection
  useEffect(() => {
    if (isListeningTest && showTranscript) {
      document.addEventListener('mouseup', handleTranscriptTextSelection);
      document.addEventListener('keyup', handleTranscriptTextSelection);

      return () => {
        document.removeEventListener('mouseup', handleTranscriptTextSelection);
        document.removeEventListener('keyup', handleTranscriptTextSelection);
      };
    }
  }, [isListeningTest, showTranscript]);

  // Extract questions safely after data is confirmed to exist
  const questions = isGroupTest 
    ? (testData as any)?.group_data || []
    : isMockExam
    ? (() => {
        // For mock exams, ensure proper sorting by question_number and handle question_data
        const mockQuestions = (testData as any)?.questions || [];
        return mockQuestions.sort((a: any, b: any) => {
          const aNum = parseInt(a.question_number) || 0;
          const bNum = parseInt(b.question_number) || 0;
          return aNum - bNum;
        });
      })()
    : testData as unknown as ApiQuestion[];

  // Check bookmark statuses for all questions using efficient batch API
  const checkAllBookmarkStatuses = useCallback(async () => {
    if (!questions || questions.length === 0) {
      return;
    }
    
    // Prevent multiple simultaneous calls
    if (isLoadingBookmarks) {
      return;
    }
    
    setIsLoadingBookmarks(true);
    
    // Collect all valid question IDs and create mapping
    const questionIdMap: Record<string, number> = {}; // questionId -> questionNumber
    const validQuestionIds: string[] = [];
    
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      const questionNumber = i + 1;
      
      if (question?.question_id && question.question_id.length === 36) {
        validQuestionIds.push(question.question_id);
        questionIdMap[question.question_id] = questionNumber;
      }
    }
    
    if (validQuestionIds.length === 0) {
      // No valid question IDs, set all to false
      const statuses: Record<number, boolean> = {};
      for (let i = 1; i <= questions.length; i++) {
        statuses[i] = false;
      }
      setBookmarkStatuses(statuses);
      return;
    }
    
    try {
      // Single batch API call instead of multiple individual calls
      const response = await testApi.checkBookmarkStatusBatch(validQuestionIds);
      
      // Build status mapping by question number
      const statuses: Record<number, boolean> = {};
      
      // Initialize all questions as not bookmarked
      for (let i = 1; i <= questions.length; i++) {
        statuses[i] = false;
      }
      
      // Update with actual bookmark statuses
      Object.entries(response.bookmarks).forEach(([questionId, isBookmarked]) => {
        const questionNumber = questionIdMap[questionId];
        if (questionNumber) {
          statuses[questionNumber] = isBookmarked;
        }
      });
      
      // Update state with all bookmark statuses at once
      setBookmarkStatuses(statuses);
      
    } catch (error) {
      // On error, set all questions as not bookmarked
      const statuses: Record<number, boolean> = {};
      for (let i = 1; i <= questions.length; i++) {
        statuses[i] = false;
      }
      setBookmarkStatuses(statuses);
    } finally {
      setIsLoadingBookmarks(false);
    }
  }, [questions]);

  // Load bookmark statuses when questions are available and user is ready
  useEffect(() => {
    if (questions && questions.length > 0 && user) {
      // Load bookmark statuses immediately when questions are available
      // Don't wait for isQuestionInitialized to ensure bookmarks appear right away
      checkAllBookmarkStatuses();
    }
  }, [questions?.length, user?.id]); // Only depend on questions length and user ID to prevent excessive calls

  // Listen for bookmark refresh events to update navigation panel
  useEffect(() => {
    const handleRefreshBookmarks = () => {
      if (questions && questions.length > 0 && !isLoadingBookmarks) {
        checkAllBookmarkStatuses();
      }
    };

    window.addEventListener('refreshBookmarks', handleRefreshBookmarks);
    return () => window.removeEventListener('refreshBookmarks', handleRefreshBookmarks);
  }, [questions?.length]); // Only depend on questions length to prevent excessive re-registrations

  // Auto-play functionality when audio ends (defined after questions)
  const handleAudioEnd = useCallback(() => {
    if (settings.autoPlayEnabled && questions && Array.isArray(questions) && currentQuestion !== null && currentQuestion < questions.length - 1) {
      // Add a small delay for better UX, then advance to next question
      setTimeout(() => {
        setCurrentQuestion(prev => (prev ?? 0) + 1);
        setShowTranscript(false);
        setActiveChunk(null);

        // Clear listening translation state when auto-advancing
        if (isListeningTest) {
          setSelectedTranscriptText('');
          setShowTranslateButton(false);
          setTranslationResult(null);
          window.getSelection()?.removeAllRanges();
        }
      }, 1500);
    }
  }, [settings.autoPlayEnabled, currentQuestion, questions, isListeningTest]);

  // Get current question with proper data extraction for mock exams
  const currentQ = (() => {
    // Don't try to get question if currentQuestion is null (not initialized yet)
    if (currentQuestion === null) return null;

    const baseQuestion = questions?.[currentQuestion];
    if (!baseQuestion) return null;
    
    if (isMockExam) {
      // For mock exams, merge question_data fields into the main question object
      const questionData = baseQuestion.question_data || {};
      return {
        ...baseQuestion,
        // Override with question_data fields for asset paths
        image_path: questionData.image_path || baseQuestion.image_path,
        audio_path: questionData.audio_path || baseQuestion.audio_path,
        extracted_text: questionData.extracted_text || baseQuestion.extracted_text,
        question_text: questionData.question_text || baseQuestion.question_text,
        choices: questionData.choices || baseQuestion.choices,
        chunks: questionData.chunks || baseQuestion.chunks,
      };
    }
    
    return baseQuestion;
  })();

  // Simple media hooks for listening tests
  const mediaSection = isFree ? `${section}_free` : section;

  const {
    mediaUrl: audioUrl,
    isLoading: isAudioLoading,
    error: audioError
  } = useAudio(
    isListeningTest && currentQ?.audio_path ? currentQ.audio_path : null,
    mediaSection,
    testIdentifier || ''
  );

  const {
    mediaUrl: imageUrl,
    isLoading: isImageLoading,
    error: imageError
  } = useImage(
    isListeningTest && currentQ?.image_path ? currentQ.image_path : null,
    mediaSection,
    testIdentifier || ''
  );

  // Cleanup chunk listener on unmount or audio change
  useEffect(() => {
    return () => {
      if (currentChunkListenerRef.current && audioRef.current?.audioElement) {
        audioRef.current.audioElement.removeEventListener('timeupdate', currentChunkListenerRef.current);
        currentChunkListenerRef.current = null;
      }
    };
  }, [audioUrl]); // Clean up when audio changes

  // NOW WE CAN HAVE EARLY RETURNS AFTER ALL HOOKS ARE DECLARED
  if (isLoading || isLoadingInfo) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Loader size="xl" color="blue" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.tests')}</Text>
          <Text size="sm" c="dimmed">{t('common.pleaseWait')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error || !testData) {
    console.error('🚨 Test loading error:', { error, testData, derivedSection: section, testIdentifier, currentPath: location.pathname });
    return (
      <Container size="lg" py="xl">
        <Alert color="red">
          {t('test.loading.error')}
          <br />{t('test.loading.section')} {section || '(vide)'}
          <br />{t('test.loading.test')} {testIdentifier || '(vide)'}
          <br />{t('test.loading.currentPath')} {location.pathname}
          <br />{t('test.loading.errorMessage')} {error?.message || t('test.loading.noDataFound')}
          <br />
          <br />💡 {t('test.loading.urlStructure')}
          <br />• {t('test.loading.forTests')} /{section || 'reading'}/{testId || 'test12'}
          <br />• {t('test.loading.forLevels')} /{section || 'reading'}/group/{groupId || '1'}
          <br />
          <Button 
            onClick={() => navigate('/')}
            size="sm"
            mt="sm"
            mr="sm"
          >
            {t('test.loading.returnHome')}
          </Button>
          <Button 
            onClick={() => navigate(`/${section || 'reading'}`)}
            size="sm"
            mt="sm"
            variant="outline"
          >
            {t('test.loading.viewTests')} {section || 'reading'}
          </Button>
        </Alert>
      </Container>
    );
  }

  if (!questions || questions.length === 0) {
    console.error('🚨 No questions found:', { questions, testData, isGroupTest });
    return (
      <Container size="lg" py="xl">
        <Alert color="orange">
          {t('test.noQuestionsFound')}
          <br />{t('test.loading.data')} {JSON.stringify(testData, null, 2).substring(0, 200)}...
        </Alert>
      </Container>
    );
  }

  // Safety check for current question
  if (!currentQ) {
    // If currentQuestion is null, we're still initializing
    if (currentQuestion === null) {
      return (
        <Container size="lg" py="xl">
          <Center>
            <Loader size="lg" />
          </Center>
        </Container>
      );
    }

    console.error('🚨 Current question not found:', {
      currentQuestion,
      questionsLength: questions?.length,
      testIdentifier,
      isGroupTest,
      isMockExam,
      section,
      testData: testData ? 'loaded' : 'not loaded'
    });
    return (
      <Container size="lg" py="xl">
        <Alert color="orange">
          {t('test.questionNotFound')} {currentQuestion + 1}
          <br />{t('test.loading.totalQuestions')} {questions?.length || 0}
          <br />{t('test.loading.currentQuestion')} {currentQuestion}
          <br />{t('test.loading.test')} {testIdentifier} ({isGroupTest ? 'group' : isMockExam ? 'mock' : 'regular'})
          <br />{t('test.loading.section')} {section}
          <br />
          <Button 
            onClick={() => setCurrentQuestion(0)}
            size="sm"
            mt="sm"
          >
            {t('test.loading.returnToQuestion')} 1
          </Button>
        </Alert>
      </Container>
    );
  }

  // Get group name for display
  const getGroupName = (groupId: string) => {
    const groupMap: Record<string, string> = {
      '1': 'A1',
      '2': 'A2', 
      '3': 'B1',
      '4': 'B2',
      '5': 'C1',
      '6': 'C2'
    };
    return groupMap[groupId] || `Groupe ${groupId}`;
  };

  const getAssetUrl = (path: string) => {
    if (!path) return '';
    
    if (isGroupTest) {
      // For group tests, parse the full path to extract asset directory, media directory and filename
      // Path format: "listening_asset_free/media_test2/Q1.mp3" or "listening_asset/media_test28/Q1.mp3"
      const pathParts = path.split('/');
      if (pathParts.length >= 3) {
        const assetDir = pathParts[0]; // e.g., "listening_asset_free" or "listening_asset"
        const mediaDir = pathParts[1]; // e.g., "media_test2"
        const filename = pathParts[2]; // e.g., "Q1.mp3"
        
        // Determine the correct section based on the asset directory
        let assetSection = section;
        if (assetDir.endsWith('_free')) {
          assetSection = section + '_free'; // e.g., "listening_free"
        }
        
        // Use the media directory as the "test identifier" for the backend
        return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${assetSection}/${mediaDir}/${filename}`;
      } else {
        // Fallback: just use the filename
      const filename = path.split('/').pop();
      return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${section}/${testIdentifier}/${filename}`;
      }
    } else if (isMockExam) {
      // For mock exams, parse the full path to extract asset directory, media directory and filename
      // Path format: "listening_asset/media_test2/Q4.mp3" or "listening_asset_free/media_test1/Q3.mp3"
      const pathParts = path.split('/');
      if (pathParts.length >= 3) {
        const assetDir = pathParts[0]; // e.g., "listening_asset" or "listening_asset_free"
        const mediaDir = pathParts[1]; // e.g., "media_test2"
        const filename = pathParts[2]; // e.g., "Q4.mp3"
        
        // Determine the correct section based on the asset directory
        let assetSection = section;
        if (assetDir.endsWith('_free')) {
          assetSection = section + '_free'; // e.g., "listening_free"
        }
        
        // Use the media directory as the "test identifier" for the backend
        return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${assetSection}/${mediaDir}/${filename}`;
      } else {
        // Fallback: just use the filename with mock exam identifier
      const filename = path.split('/').pop();
      return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${section}/${testIdentifier}/${filename}`;
      }
    } else {
      // Regular tests - construct the URL based on test ID and whether it's free
      const filename = path.split('/').pop();
      const assetSection = isFree ? `${section}_free` : section;
      return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${assetSection}/${testIdentifier}/${filename}`;
    }
  };

  return (
    <Box style={{ display: 'flex' }}>
      {/* Main Content Area */}
      <Box style={{ 
        flex: 1, 
        overflow: 'auto', 
        position: 'relative'
      }}>
        <Container size="lg" py="xl" style={{ position: 'relative' }}>
          {/* Header with timer */}
          <Box mb="xl">
            <Group justify="space-between" align="center">
              <Group>
                <Button 
                  variant="outline" 
                  leftSection={<IconArrowLeft size={16} />}
                  onClick={() => navigate(`/${section}`)}
                >
                  {t('test.loading.returnTests')}
                </Button>
                <Title order={1}>
                  {isGroupTest ? (
                    <>
                      {t('test.loading.practiceByLevel')} - {getGroupName(groupId!)} - {section === 'listening' ? t('test.loading.oralComprehension') : 
                                 section === 'reading' ? t('test.loading.writtenComprehension') :
                                 section === 'writing' ? t('test.loading.writtenExpression') : t('test.loading.oralExpression')}
                    </>
                  ) : isMockExam ? (
                    <>
                      {t('test.loading.mockExam')} - {section === 'listening' ? t('test.loading.oralComprehension') : 
                                     section === 'reading' ? t('test.loading.writtenComprehension') :
                                     section === 'writing' ? t('test.loading.writtenExpression') : t('test.loading.oralExpression')}
                    </>
                  ) : (
                    <>
                      {t('test.loading.test')} {formatTestId(testId)} - {section === 'listening' ? t('test.loading.oralComprehension') : 
                                       section === 'reading' ? t('test.loading.writtenComprehension') :
                                       section === 'writing' ? t('test.loading.writtenExpression') : t('test.loading.oralExpression')}
                      {isFree && <Text span c="green" ml="sm">(Gratuit)</Text>}
                    </>
                  )}
                </Title>
              </Group>
            </Group>
          </Box>

          {/* Navigation Panel */}
          {isGroupTest ? (
            // Group test navigation - same format as normal tests
            <Paper
              p="lg"
              mb="xl"
              shadow="sm"
              radius="md"
              style={{
                backgroundColor: themeColors.surface,
                border: `1px solid ${themeColors.border}`,
              }}
            >
              <Text fw={600} mb="sm" c={themeColors.textPrimary}>{t('test.loading.questionNavigation')}</Text>
              <Group gap="xs" mb="md">
                <Group gap="xs">
                  <Box
                    w={18}
                    h={18}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed',
                      border: `2px solid ${themeColors.reading}`,
                      borderRadius: 4
                    }}
                  />
                  <Text size="sm" c={themeColors.textPrimary}>{t('test.loading.answered')}</Text>
                </Group>
                <Group gap="xs">
                  <Box
                    w={18}
                    h={18}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0',
                      border: `2px solid ${themeColors.speaking}`,
                      borderRadius: 4
                    }}
                  />
                  <Text size="sm" c={themeColors.textPrimary}>{t('test.loading.wrongAnswer')}</Text>
                </Group>
                <Group gap="xs">
                  <Box
                    w={18}
                    h={18}
                    style={{
                      backgroundColor: themeColors.surfaceHover,
                      border: `2px solid ${themeColors.border}`,
                      borderRadius: 4
                    }}
                  />
                  <Text size="sm" c={themeColors.textPrimary}>{t('test.loading.unanswered')}</Text>
              </Group>
                </Group>
                
              {/* All questions on one line */}
                <Group gap="xs">
                {Array.from({ length: questions.length }, (_, i) => i + 1).map((questionNum) => {
                  const index = questionNum - 1;
                    const isAnswered = answers[questionNum.toString()] !== undefined;
                    const isWrong = wrongQuestions.includes(questionNum);
                  const isActive = index === currentQuestion;
                  const isBookmarked = bookmarkStatuses[questionNum] || false;
                  
                    
                    return (
                      <Box key={questionNum} style={{ position: 'relative' }}>
                        <Button
                          size="xs"
                        w={28}
                        h={28}
                          p={0}
                          variant={isActive ? "filled" : "outline"}
                          color={isWrong ? "red" : isAnswered ? "green" : isActive ? "blue" : "gray"}
                        onClick={() => goToQuestion(index)}
                          style={{
                            backgroundColor: isWrong
                              ? (resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0')
                              : isAnswered
                                ? (resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed')
                                : isActive
                                  ? themeColors.primary
                                  : themeColors.surfaceHover,
                            color: isWrong
                              ? themeColors.speaking
                              : isAnswered
                                ? themeColors.reading
                                : isActive
                                  ? '#fff'
                                  : themeColors.textPrimary,
                            border: `2px solid ${isWrong
                              ? themeColors.speaking
                              : isAnswered
                                ? themeColors.reading
                                : isActive
                                  ? themeColors.primary
                                  : themeColors.border}`,
                          }}
                        >
                          {questionNum}
                        </Button>
                        {isBookmarked && (
                          <IconStarFilled
                            size={10}
                            style={{
                              position: 'absolute',
                              top: -2,
                              right: -2,
                              color: '#ffc107',
                              filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))',
                              zIndex: 10
                            }}
                          />
                        )}
                      </Box>
                    );
                  })}
                </Group>
            </Paper>
          ) : (
            // Regular test navigation with TCF level groups
            <Paper
              p="lg"
              mb="xl"
              shadow="sm"
              radius="md"
              style={{
                backgroundColor: themeColors.surface,
                border: `1px solid ${themeColors.border}`,
              }}
            >
              <Text fw={600} mb="sm" c={themeColors.textPrimary}>{t('test.loading.questionNavigation')}</Text>
              <Group gap="xs" mb="md">
                <Group gap="xs">
                  <Box
                    w={18}
                    h={18}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed',
                      border: `2px solid ${themeColors.reading}`,
                      borderRadius: 4
                    }}
                  />
                  <Text size="sm" c={themeColors.textPrimary}>{t('test.loading.answered')}</Text>
                </Group>
                <Group gap="xs">
                  <Box
                    w={18}
                    h={18}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0',
                      border: `2px solid ${themeColors.speaking}`,
                      borderRadius: 4
                    }}
                  />
                  <Text size="sm" c={themeColors.textPrimary}>{t('test.loading.wrongAnswer')}</Text>
                </Group>
                <Group gap="xs">
                  <Box
                    w={18}
                    h={18}
                    style={{
                      backgroundColor: themeColors.surfaceHover,
                      border: `2px solid ${themeColors.border}`,
                      borderRadius: 4
                    }}
                  />
                  <Text size="sm" c={themeColors.textPrimary}>{t('test.loading.unanswered')}</Text>
                </Group>
            </Group>
              
              {/* Row 1: A1, A2, B1 */}
              <Flex justify="space-between" mb="lg">
                {navGroups.slice(0, 3).map((group) => (
                  <Box key={group.label}>
                    <Text fw={600} c={themeColors.textPrimary} size="sm" mb="xs">
                      {group.label} • {group.points} {t('navigation.pointsEach')}
                    </Text>
                    <Group gap="md">
                      {Array.from(
                        { length: group.range[1] - group.range[0] + 1 },
                        (_, i) => group.range[0] + i
                      ).map((questionNum) => {
                        if (questionNum - 1 >= questions.length) return null;
                        const index = questionNum - 1;
                        const isAnswered = answers[(questionNum).toString()] !== undefined;
                        const isWrong = wrongQuestions.includes(questionNum);
                        const isActive = index === currentQuestion;
                        const isBookmarked = bookmarkStatuses[questionNum] || false;
                        
                        return (
                          <Box key={questionNum} style={{ position: 'relative' }}>
                            <Button
                              size="xs"
                              w={28}
                              h={28}
                              p={0}
                              variant={isActive ? "filled" : "outline"}
                              color={isWrong ? "red" : isAnswered ? "green" : isActive ? "blue" : "gray"}
                              onClick={() => goToQuestion(index)}
                              style={{
                                backgroundColor: isWrong
                                  ? (resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0')
                                  : isAnswered
                                    ? (resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed')
                                    : isActive
                                      ? themeColors.primary
                                      : themeColors.surfaceHover,
                                color: isWrong
                                  ? themeColors.speaking
                                  : isAnswered
                                    ? themeColors.reading
                                    : isActive
                                      ? '#fff'
                                      : themeColors.textPrimary,
                                border: `2px solid ${isWrong
                                  ? themeColors.speaking
                                  : isAnswered
                                    ? themeColors.reading
                                    : isActive
                                      ? themeColors.primary
                                      : themeColors.border}`,
                              }}
                            >
                              {questionNum}
                            </Button>
                            {isBookmarked && (
                              <IconStarFilled
                                size={10}
                                style={{
                                  position: 'absolute',
                                  top: -2,
                                  right: -2,
                                  color: '#ffc107',
                                  filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))',
                                  zIndex: 10
                                }}
                              />
                            )}
                          </Box>
                        );
                      })}
                    </Group>
                  </Box>
                ))}
              </Flex>

              {/* Row 2: B2, C1, C2 */}
              <Flex justify="space-between">
                {navGroups.slice(3).map((group) => (
                  <Box key={group.label}>
                    <Text fw={600} c={themeColors.textPrimary} size="sm" mb="xs">
                      {group.label} • {group.points} {t('navigation.pointsEach')}
                    </Text>
                    <Group gap="md">
                      {Array.from(
                        { length: group.range[1] - group.range[0] + 1 },
                        (_, i) => group.range[0] + i
                      ).map((questionNum) => {
                        if (questionNum - 1 >= questions.length) return null;
                        const index = questionNum - 1;
                        const isAnswered = answers[(questionNum).toString()] !== undefined;
                        const isWrong = wrongQuestions.includes(questionNum);
                        const isActive = index === currentQuestion;
                        const isBookmarked = bookmarkStatuses[questionNum] || false;
                        
                        return (
                          <Box key={questionNum} style={{ position: 'relative' }}>
                            <Button
                              size="xs"
                              w={28}
                              h={28}
                              p={0}
                              variant={isActive ? "filled" : "outline"}
                              color={isWrong ? "red" : isAnswered ? "green" : isActive ? "blue" : "gray"}
                              onClick={() => goToQuestion(index)}
                              style={{
                                backgroundColor: isWrong
                                  ? (resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0')
                                  : isAnswered
                                    ? (resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed')
                                    : isActive
                                      ? themeColors.primary
                                      : themeColors.surfaceHover,
                                color: isWrong
                                  ? themeColors.speaking
                                  : isAnswered
                                    ? themeColors.reading
                                    : isActive
                                      ? '#fff'
                                      : themeColors.textPrimary,
                                border: `2px solid ${isWrong
                                  ? themeColors.speaking
                                  : isAnswered
                                    ? themeColors.reading
                                    : isActive
                                      ? themeColors.primary
                                      : themeColors.border}`,
                              }}
                            >
                              {questionNum}
                            </Button>
                            {isBookmarked && (
                              <IconStarFilled
                                size={10}
                                style={{
                                  position: 'absolute',
                                  top: -2,
                                  right: -2,
                                  color: '#ffc107',
                                  filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))',
                                  zIndex: 10
                                }}
                              />
                            )}
                          </Box>
                        );
                      })}
                    </Group>
                  </Box>
                ))}
              </Flex>
            </Paper>
          )}
          <Box style={{ flex: 'flex-grow', flexWrap: 'nowrap', display: 'flex', justifyContent: 'center'}}>
            {/* Analysis - Extends from button's middle position */}
            {notebookExpanded.leftExpanded && (
              <Box
                style={{
                  zIndex: 10,
                  padding: '1rem',
                }}
              >
                <AnalysisSidebar
                  section={section}
                  testId={testIdentifier || ''}
                  currentQuestion={(currentQuestion ?? 0) + 1}
                  isGroupTest={isGroupTest}
                  isFree={isFree}
                  onToggle={toggleAnalysis}
                  isExpanded={notebookExpanded.leftExpanded} />
              </Box>
            )}
            {/* Question Content */}
            <Paper
              p="xl"
              shadow="sm"
              radius="md"
              mb="xl"
              style={{
                position: 'relative',
                flex: 1,
                backgroundColor: themeColors.surface,
                border: `1px solid ${themeColors.border}`,
              }}
            >
              {/* Analysis Sidebar - For reading and listening tests */}
              {(section === 'reading' || section === 'listening') && !isLoading && !isLoadingInfo && testData && isQuestionInitialized && (
                (
                  <Box
                    onClick={toggleAnalysis}
                    style={{
                      position: 'absolute',
                      left: notebookExpanded.leftExpanded ? '-20px' : '-20px', // Move with notebook when expanded
                      top: '50%', // Same vertical position always
                      transform: 'translateY(-50%)',
                      width: '40px',
                      height: '40px',
                      backgroundColor: themeColors.primary,
                      border: `2px solid ${themeColors.primaryHover}`,
                      borderRadius: '50%',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 20,
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = themeColors.primaryHover;
                      e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';
                      e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                        ? '0 6px 16px rgba(30, 144, 255, 0.4)'
                        : '0 6px 16px rgba(0, 123, 255, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = themeColors.primary;
                      e.currentTarget.style.transform = 'translateY(-50%)';
                      e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                        ? '0 4px 12px rgba(30, 144, 255, 0.3)'
                        : '0 4px 12px rgba(0, 123, 255, 0.3)';
                    }}
                  >
                    {notebookExpanded.leftExpanded ? (
                      <IconChevronLeft size={20} color="white" />
                    ) : (
                      <IconBrain size={20} color="white" />
                    )}
                  </Box>
                )
              )}

              {/* Simple Notebook Toggle Button - Horizontal movement only */}
              <Box
                onClick={toggleNotebook}
                style={{
                  position: 'absolute',
                  right: notebookExpanded.rightExpanded ? '-20px' : '-20px', // Move with notebook when expanded
                  top: '50%', // Same vertical position always
                  transform: 'translateY(-50%)',
                  width: '40px',
                  height: '40px',
                  backgroundColor: themeColors.primary,
                  border: `2px solid ${themeColors.primaryHover}`,
                  borderRadius: '50%',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 20,
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = themeColors.primaryHover;
                  e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';
                  e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                    ? '0 6px 16px rgba(30, 144, 255, 0.4)'
                    : '0 6px 16px rgba(0, 123, 255, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = themeColors.primary;
                  e.currentTarget.style.transform = 'translateY(-50%)';
                  e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                    ? '0 4px 12px rgba(30, 144, 255, 0.3)'
                    : '0 4px 12px rgba(0, 123, 255, 0.3)';
                }}
              >
                {notebookExpanded.rightExpanded ? (
                  <IconChevronRight size={20} color="white" />
                ) : (
                  <IconNotebook size={20} color="white" />
                )}
              </Box>



              {/* Bookmark Icon */}
              {currentQ?.question_id && currentQ.question_id.length === 36 && (
                <BookmarkIcon
                  questionId={currentQ.question_id}
                  size="md"
                />
              )}

              <Stack gap="md">
                <Text fw={600} size="xl">{t('test.loading.question')} {(currentQuestion ?? 0) + 1}</Text>

                {/* Question appearances - show for group tests and mock exams, hide for normal tests */}
                {(isGroupTest || isMockExam) && currentQ?.locations && currentQ.locations.length > 0 && (
                  <Text size="sm" c="dimmed" style={{ fontStyle: 'italic', marginTop: '-0.5rem', marginBottom: '1rem' }}>
                    {t('test.loading.appearedIn')} {currentQ.locations.join(', ')}
                  </Text>
                )}

                {/* DO NOT show source test info for normal individual tests - users don't need this information */}

              {/* Image - Optimized to prevent layout shifts */}
              {isListeningTest && currentQ.image_path && (
                <Group justify="center">
                  <OptimizedImage
                    src={imageUrl}
                    alt={`Question ${(currentQuestion ?? 0) + 1}`}
                    isLoading={isImageLoading}
                    error={imageError}
                    maxWidth={400}
                    maxHeight={320}
                    aspectRatio={4/3}
                  />
                </Group>
              )}

              {/* Audio Player - Persistent across questions */}
              {isListeningTest && currentQ.audio_path && (
                <>
                  {audioError ? (
                    <Alert color="red" title="Audio Error" style={{ margin: '1.2rem 0 1.5rem 0' }}>
                      {audioError}
                    </Alert>
                  ) : (
                    <CustomAudioPlayer
                      ref={audioRef}
                      src={audioUrl || ''}
                      onEnded={handleAudioEnd}
                      playbackSpeed={settings.defaultPlaybackSpeed}
                      onPlaybackSpeedChange={setPlaybackSpeed}
                      showAutoPlayToggle={true}
                      autoPlayEnabled={settings.autoPlayEnabled}
                      onAutoPlayToggle={toggleAutoPlay}
                      shouldAutoPlayOnLoad={settings.autoPlayEnabled}
                      isLoading={isAudioLoading}
                      style={{ margin: '1.2rem 0 1.5rem 0' }}
                    />
                  )}
                </>
              )}

              {/* Reading Passage - Robust text display system */}
              {!isListeningTest && currentQ.extracted_text && (() => {
                const { displayText, useFlexibleWrapping, hasOCRFormat } = prepareTextForDisplay(currentQ.extracted_text);

                // Create responsive text styling that prevents wide text while preserving OCR format
                const getTextContainerStyle = () => ({
                  display: 'flex',
                  justifyContent: 'center',
                  width: '100%',
                  padding: '0 20px', // Mobile-friendly padding
                  // Ensure no horizontal overflow
                  overflow: 'hidden',
                });

                const getTextStyle = () => ({
                  fontFamily: 'system-ui, -apple-system, sans-serif',
                  fontSize: '16px',
                  lineHeight: 1.7,
                  textAlign: 'left' as const,
                  color: themeColors.textPrimary,
                  maxWidth: '800px',
                  width: '100%',

                  // Core text wrapping strategy - always use flexible wrapping to prevent wide text
                  whiteSpace: 'pre-wrap' as const,
                  overflowWrap: 'break-word' as const,
                  wordBreak: 'break-word' as const,

                  // Ensure text never exceeds container width
                  boxSizing: 'border-box' as const,
                });

                return (
                  <Box style={getTextContainerStyle()}>
                    {/* Text content with highlighting and translation support */}
                    {shouldShowHighlighting ? (
                      <UnifiedTextInteraction
                        mode="passage"
                        text={displayText}
                        highlights={highlights}
                        onAddHighlight={addHighlight}
                        onRemoveHighlight={removeHighlight}
                        style={getTextStyle()}
                        questionIndex={currentQuestion ?? undefined}
                      />
                    ) : (
                      <Text style={getTextStyle()}>
                        {displayText}
                      </Text>
                    )}
                  </Box>
                );
              })()}

              {/* Question Text - For reading tests, show the actual question */}
              {!isListeningTest && currentQ.question_text && (
                <Box style={{ textAlign: 'center', marginTop: '2rem' }}>
                  <UnifiedTextInteraction mode="question" size="xl" fw={700} style={{ color: themeColors.textPrimary }} questionIndex={currentQuestion ?? undefined}>
                    {currentQ.question_text}
                  </UnifiedTextInteraction>
                </Box>
              )}

              {/* For listening tests, don't show any text content - only audio, image, and choices */}

              {/* Choices */}
              {currentQ.choices && (
                <Box>
                  <Radio.Group
                    value={currentQuestion !== null ? (answers[(currentQuestion + 1).toString()] as string || '') : ''}
                    onChange={(value) => currentQuestion !== null && handleAnswerChange((currentQuestion + 1).toString(), value)}
                  >
                    <Stack gap="lg">
                      {Object.entries(currentQ.choices).map(([letter, text]) => {
                        const isSelected = currentQuestion !== null && answers[(currentQuestion + 1).toString()] === letter;
                        return (
                          <Box
                            key={letter}
                            style={{
                              cursor: 'pointer',
                              borderRadius: '16px',
                              border: `3px solid ${isSelected ? themeColors.primary : themeColors.border}`,
                              transition: 'all 0.3s ease',
                              backgroundColor: isSelected
                                ? (resolvedTheme === 'dark' ? '#1a2332' : '#f0f8ff')
                                : themeColors.surface,
                              boxShadow: isSelected
                                ? `0 6px 20px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.2)' : 'rgba(34, 139, 230, 0.15)'}`
                                : `0 2px 8px ${resolvedTheme === 'dark' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.06)'}`,
                              transform: isSelected ? 'translateY(-2px)' : 'none'
                            }}
                            onClick={() => {
                              if (currentQuestion !== null) {
                                handleAnswerChange((currentQuestion + 1).toString(), letter);
                                handleSave(); // Save when a choice is clicked
                              }
                            }}
                            onMouseEnter={(e) => {
                              if (!isSelected) {
                                e.currentTarget.style.borderColor = themeColors.primary;
                                e.currentTarget.style.backgroundColor = themeColors.surfaceHover;
                                e.currentTarget.style.transform = 'translateY(-1px)';
                                e.currentTarget.style.boxShadow = `0 4px 12px ${resolvedTheme === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.1)'}`;
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (!isSelected) {
                                e.currentTarget.style.borderColor = themeColors.border;
                                e.currentTarget.style.backgroundColor = themeColors.surface;
                                e.currentTarget.style.transform = 'none';
                                e.currentTarget.style.boxShadow = `0 2px 8px ${resolvedTheme === 'dark' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.06)'}`;
                              }
                            }}
                          >
                            <Group gap="xl" p={text ? "md" : "xs"} wrap="nowrap" align="center">
                              <Box
                                style={{
                                  minWidth: '48px',
                                  height: '48px',
                                  borderRadius: '50%',
                                  backgroundColor: isSelected ? themeColors.primary : themeColors.surfaceHover,
                                  color: isSelected ? 'white' : themeColors.textPrimary,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontWeight: 'bold',
                                  fontSize: '20px',
                                  transition: 'all 0.3s ease',
                                  border: isSelected ? 'none' : `2px solid ${themeColors.border}`
                                }}
                              >
                                {letter}
                              </Box>
                              <Text
                                size="lg"
                                style={{
                                  lineHeight: 1.6,
                                  color: isSelected ? themeColors.primary : themeColors.textPrimary,
                                  fontWeight: isSelected ? 500 : 400,
                                  flex: 1
                                }}
                              >
                                {String(text)}
                              </Text>
                              <Radio
                                value={letter}
                                style={{ visibility: 'hidden', width: 0, margin: 0 }}
                              />
                            </Group>
                          </Box>
                        );
                      })}
                    </Stack>
                  </Radio.Group>
                </Box>
              )}

              {/* Interactive Transcript */}
              {isListeningTest && currentQ.chunks && (
                <Box>
                  <Group gap="sm">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowTranscript(!showTranscript);
                        // Clear translation when hiding transcript
                        if (showTranscript) {
                          setTranslationResult(null);
                          setShowTranslateButton(false);
                          setSelectedTranscriptText('');
                          window.getSelection()?.removeAllRanges();
                        }
                      }}
                      leftSection={showTranscript ? <IconEyeOff size={16} /> : <IconEye size={16} />}
                    >
                      {showTranscript ? t('test.loading.hideOriginalText') : t('test.loading.showOriginalText')}
                    </Button>

                    {showTranslateButton && (
                      <Group gap={0} style={{ position: 'relative' }}>
                        <Button
                          variant="outline"
                          color="blue"
                          leftSection={<IconLanguage size={16} />}
                          onClick={handleTranslateText}
                          loading={isTranslating}
                          disabled={isTranslating}
                          style={{
                            borderTopRightRadius: 0,
                            borderBottomRightRadius: 0,
                            borderRight: 'none',
                          }}
                        >
                          {isTranslating ? 'Translating...' : 'Translate'}
                        </Button>

                        {/* Language switcher dropdown-style button */}
                        <Button
                          variant="outline"
                          color="blue"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleTranslationTarget();
                          }}
                          style={{
                            borderTopLeftRadius: 0,
                            borderBottomLeftRadius: 0,
                            minWidth: '50px',
                            padding: '0 8px',
                            fontSize: '12px',
                            fontWeight: 'bold',
                          }}
                          title={`Switch to ${translationTarget === 'en' ? 'Chinese' : 'English'}`}
                        >
                          {translationTarget === 'en' ? 'EN' : '中文'}
                        </Button>
                      </Group>
                    )}
                  </Group>

                  {/* Translation Result Display */}
                  {translationResult && (
                    <Box mt="md" p="md" style={{
                      borderRadius: '8px',
                      border: `1px solid ${themeColors.primary}`,
                      backgroundColor: themeColors.background
                    }}>
                      <Text size="sm" style={{ lineHeight: 1.6 }}>
                        {translationResult.text}
                      </Text>
                    </Box>
                  )}

                  {showTranscript && (
                    <Box mt="md">
                      <Button
                        size="sm"
                        variant="outline"
                        mb="md"
                        onClick={toggleAllTranscript}
                      >
                        {showAllTranscript ? t('test.loading.hideAllText') : t('test.loading.showAllText')}
                      </Button>
                      
                      <Stack gap="xs" className="transcript-container">
                        {currentQ.chunks?.map((chunk: any, index: number) => (
                          <Card
                            key={index}
                            p="sm"
                            className="transcript-card"
                            data-transcript="true"
                            style={{
                              backgroundColor: activeChunk === chunk.start
                                ? (resolvedTheme === 'dark' ? '#1a4d00' : '#fff78a')
                                : themeColors.surfaceHover,
                              cursor: 'pointer',
                              transition: 'background 0.2s',
                              border: activeChunk === chunk.start
                                ? `3px solid ${resolvedTheme === 'dark' ? '#4caf50' : '#ffc107'}`
                                : `1px solid ${themeColors.border}`,
                              boxShadow: activeChunk === chunk.start
                                ? (resolvedTheme === 'dark' ? '0 0 10px rgba(76, 175, 80, 0.5)' : '0 0 10px rgba(255, 193, 7, 0.5)')
                                : 'none'
                            }}
                            onClick={() => playChunk(chunk)}
                          >
                            <div style={{ 
                              display: 'flex', 
                              alignItems: 'center', 
                              gap: '8px', 
                              width: '100%',
                              flexWrap: 'nowrap'
                            }}>
                              <Button
                                size="xs"
                                variant="subtle"
                                style={{
                                  flexShrink: 0,
                                  minWidth: 'auto',
                                  height: '24px',
                                  padding: '4px'
                                }}
                                styles={{
                                  root: {
                                    color: themeColors.textPrimary,
                                    '&:hover': {
                                      backgroundColor: themeColors.surfaceHover,
                                    },
                                  },
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleTranscriptChunk(index);
                                }}
                              >
                                {transcriptVisible[index] ? <IconEye size={16} /> : <IconEyeOff size={16} />}
                              </Button>
                              {transcriptVisible[index] && (
                                <Text
                                  component="span"
                                  style={{
                                    flex: 1,
                                    fontSize: '14px',
                                    lineHeight: '1.4',
                                    color: themeColors.textPrimary
                                  }}
                                >
                                  {chunk.text}
                                </Text>
                              )}
                            </div>
                          </Card>
                        ))}
                      </Stack>
                    </Box>
                  )}
                </Box>
              )}

                {/* Navigation Buttons */}
                <Group justify="space-between" mt="xl">
                  <Button
                    variant="outline"
                    disabled={currentQuestion === null || currentQuestion === 0}
                    onClick={() => currentQuestion !== null && goToQuestion(currentQuestion - 1)}
                  >
                    {t('test.loading.previous')}
                  </Button>

                  <Button
                    variant="outline"
                    disabled={currentQuestion === null || currentQuestion === questions.length - 1}
                    onClick={() => currentQuestion !== null && goToQuestion(currentQuestion + 1)}
                  >
                    {t('test.loading.next')}
                  </Button>
                </Group>
              </Stack>
            </Paper>
            {/* Universal Notebook - Extends from button's middle position */}
            {notebookExpanded.rightExpanded && (
              <Box
                style={{
                  zIndex: 10,
                  padding: '1rem',
                }}
              >
                <NotebookSidebar isExpanded={notebookExpanded.rightExpanded} />
              </Box>
            )}
          </Box>
          {/* Submit Button */}
          <Group justify="center">
            <Button 
              size="lg"
              color="green"
              onClick={handleSubmit}
              loading={submitMutation.isPending}
            >
              {t('test.loading.submitTest')}
            </Button>
          </Group>
        </Container>
      </Box>
      {/* Add the GradingResultsModal */}
      <GradingResultsModal
        isOpen={showResultsModal}
        onClose={() => setShowResultsModal(false)}
        results={gradingResults}
        testType={gradingResults?.testType || 'reading'}
        testId={gradingResults?.testId || ''}
        questionIds={gradingResults?.questionIds}
        isGroupTest={gradingResults?.isGroupTest || false}
      />

      {/* Feature Hint for notebook and analysis buttons */}
      <FeatureHint
        showHint={showFeatureHint}
        section={section as 'reading' | 'listening'}
      />
    </Box>
  );
}