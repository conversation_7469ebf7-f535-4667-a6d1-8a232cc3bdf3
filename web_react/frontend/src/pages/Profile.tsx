import {
  Container,
  Paper,
  Title,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Grid,
  Card,
  Divider,
  Alert,
  Modal,
  PasswordInput,
  Loader,
  Box,
  Progress,
  Pagination,
  Select,
  TextInput,
  Flex,
  ActionIcon,
  Tooltip,
  Menu,
  SegmentedControl
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { useState, useEffect, useRef, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { 
  IconUser, 
  IconCreditCard, 
  IconHistory, 
  IconKey, 
  IconInfoCircle,
  IconEar,
  IconBook,
  IconPencil,
  IconMicrophone,
  IconHeadphones,
  IconPlayerPlay,
  IconProgress,
  IconLock,
  IconCheck,
  IconX,
  IconSearch,
  IconFilter,
  IconSortDescending,
  IconSortAscending,
  IconList,
  IconDots,
  IconTrash,
  IconMail,
  IconShield
} from '@tabler/icons-react';
import { authApi, testApi, paymentsApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { useThemeColors } from '../store/useThemeStore';
import { Link } from 'react-router-dom';
import { formatDateSafely, isDateExpired, formatDateOrNull, formatDateWithUserTimezone } from '../utils/dateUtils';
import { useTranslation } from 'react-i18next';

// Helper function to clean up test ID display
const formatTestId = (testId: string): string => {
  // Remove "test" prefix if it exists, keeping only the number
  const match = testId.match(/^test(\d+)$/i);
  if (match) {
    return match[1]; // Return just the number
  }
  // For group tests or other formats, return as-is
  return testId.replace(/^group/, ''); // Remove 'group' prefix for group tests
};

// Helper function to format test title
const formatTestTitle = (section: string, testId: string, t: any): string => {
  const sectionConfig = {
    listening: {
      title: t('home.sections.listening.title'),
      description: t('home.sections.listening.description'),
      color: '#007bff',
      icon: IconHeadphones
    },
    reading: {
      title: t('home.sections.reading.title'),
      description: t('home.sections.reading.description'),
      color: '#28a745',
      icon: IconBook
    },
    writing: {
      title: t('home.sections.writing.title'),
      description: t('home.sections.writing.description'),
      color: '#fd7e14',
      icon: IconPencil
    },
    speaking: {
      title: t('home.sections.speaking.title'),
      description: t('home.sections.speaking.description'),
      color: '#dc3545',
      icon: IconMicrophone
    }
  };
  
  const config = sectionConfig[section as keyof typeof sectionConfig];
  const cleanTestId = formatTestId(testId);
  
  // Special handling for group tests
  if (testId.startsWith('group')) {
    return `${config ? config.title : section} - Niveau ${cleanTestId}`;
  }
  
  return `${config ? config.title : section} - Test ${cleanTestId}`;
};

export function Profile() {
  const { user, logout, setUser, isLifetimeMember } = useAuthStore();
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const [changePasswordOpened, { open: openChangePassword, close: closeChangePassword }] = useDisclosure(false);
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false);
  const [testToDelete, setTestToDelete] = useState<any>(null);
  const previousMembershipDataRef = useRef<any>(null);
  const queryClient = useQueryClient();

  // Remove the old password form state variables
  // Add new state for email verification flow
  const [passwordResetSent, setPasswordResetSent] = useState(false);
  const [resetEmailAddress, setResetEmailAddress] = useState('');

  // Test history pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [testsPerPage, setTestsPerPage] = useState(12);
  const [searchTerm, setSearchTerm] = useState('');
  const [sectionFilter, setSectionFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date-desc');

  // Translation method preference state
  const [translationMethod, setTranslationMethod] = useState<string>('azure');

  // Initialize translation method from user profile
  useEffect(() => {
    if (user?.profile_data) {
      try {
        const profileData = JSON.parse(user.profile_data);
        setTranslationMethod(profileData.translation_method || 'azure');
      } catch (e) {
        setTranslationMethod('azure');
      }
    }
  }, [user]);

  const { data: historyData, isLoading: historyLoading } = useQuery({
    queryKey: ['test-history'],
    queryFn: testApi.getTestHistory,
  });

  // Fetch fresh membership status when profile loads
  const { data: membershipData } = useQuery({
    queryKey: ['membership-status', 'profile'],
    queryFn: paymentsApi.getMembershipStatus,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Update user data when fresh membership status is received
  useEffect(() => {
    if (membershipData?.user && user) {
      // Check if this is new data by comparing with previous data
      const isDifferentFromPrevious = 
        !previousMembershipDataRef.current ||
        previousMembershipDataRef.current.membership_type !== membershipData.user.membership_type ||
        previousMembershipDataRef.current.membership_expires_at !== membershipData.user.membership_expires_at;
      
      // Also check if it's different from current user data
      const isDifferentFromCurrentUser = 
        membershipData.user.membership_type !== user.membership_type ||
        membershipData.user.membership_expires_at !== user.membership_expires_at;
      
      if (isDifferentFromPrevious && isDifferentFromCurrentUser) {
        
        const updatedUser = {
          ...user,
          membership_type: membershipData.user.membership_type || user.membership_type,
          membership_expires_at: membershipData.user.membership_expires_at || user.membership_expires_at,
        };
        setUser(updatedUser);
        
        // Update the ref to track this data
        previousMembershipDataRef.current = {
          membership_type: membershipData.user.membership_type,
          membership_expires_at: membershipData.user.membership_expires_at,
        };
      }
    }
  }, [membershipData, setUser]); // Keep user out of dependencies to prevent infinite loop

  // Replace the old changePasswordForm and mutation with new email-based flow
  const requestPasswordResetMutation = useMutation({
    mutationFn: authApi.requestProfilePasswordReset,
    onSuccess: (data) => {
      setPasswordResetSent(true);
      setResetEmailAddress(data.email);
      notifications.show({
        title: t('profile.passwordChange.emailSent'),
        message: `${t('profile.passwordChange.linkSent')} ${data.email}`,
        color: 'green',
      });
    },
    onError: (error: any) => {
      notifications.show({
        title: t('common.error'),
        message: error.response?.data?.error || t('profile.passwordChange.sendError'),
        color: 'red',
      });
    },
  });

  // Delete test history mutation
  const deleteHistoryMutation = useMutation({
    mutationFn: () => testApi.deleteTestHistory(testToDelete.section, testToDelete.test_id, testToDelete.free),
    onSuccess: (data) => {
      notifications.show({
        title: t('profile.historyDeleted'),
        message: t('profile.historyDeleted'),
        color: 'green',
      });
      
      // Invalidate and refetch the test history data
      queryClient.invalidateQueries({ queryKey: ['test-history'] });
      closeDeleteModal();
      setTestToDelete(null);
    },
    onError: (error: any) => {
      notifications.show({
        title: t('common.error'),
        message: `${t('profile.deleteError')}: ${error.response?.data?.error || error.message}`,
        color: 'red',
      });
      closeDeleteModal();
      setTestToDelete(null);
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (profileData: { translation_method?: string }) => authApi.updateProfile(profileData),
    onSuccess: (data) => {
      // Update user in auth store
      if (user) {
        setUser({
          ...user,
          profile_data: JSON.stringify(data.profile_data)
        });
      }
    },
    onError: (error: any) => {
      notifications.show({
        title: t('common.error'),
        message: `${t('profile.updateError')}: ${error.response?.data?.error || error.message}`,
        color: 'red',
      });

      // Revert the UI state on error
      if (user?.profile_data) {
        try {
          const profileData = JSON.parse(user.profile_data);
          setTranslationMethod(profileData.translation_method || 'azure');
        } catch (e) {
          setTranslationMethod('azure');
        }
      }
    },
  });

  // Handle translation method change
  const handleTranslationMethodChange = (value: string) => {
    const previousValue = translationMethod;
    setTranslationMethod(value);
    updateProfileMutation.mutate({ translation_method: value }, {
      onSuccess: () => {
        notifications.show({
          title: t('profile.profileUpdated'),
          message: t('profile.translationMethodUpdated'),
          color: 'green',
        });
      },
      onError: () => {
        // Revert the UI state on error
        setTranslationMethod(previousValue);
        notifications.show({
          title: t('profile.updateError'),
          message: t('profile.updateError'),
          color: 'red',
        });
      }
    });
  };

  const handleRequestPasswordReset = () => {
    requestPasswordResetMutation.mutate();
  };

  const handleClosePasswordModal = () => {
    setPasswordResetSent(false);
    setResetEmailAddress('');
    closeChangePassword();
  };

  const handleLogout = async () => {
    try {
      await authApi.logout();
      logout();
      notifications.show({
        title: t('profile.logout.success'),
        message: t('auth.logout.success'),
        color: 'blue',
      });
    } catch (error) {
      notifications.show({
        title: t('common.error'),
        message: t('auth.logout.error'),
        color: 'red',
      });
    }
  };

  const handleDeleteTest = (test: any) => {
    setTestToDelete(test);
    openDeleteModal();
  };

  // Filter and sort tests
  const filteredAndSortedTests = useMemo(() => {
    if (!historyData?.history) return [];

    let filtered = historyData.history.filter((test) => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const testTitle = formatTestTitle(test.section, test.test_id, t);
        if (!testTitle.toLowerCase().includes(searchLower)) {
          return false;
        }
      }

      // Section filter
      if (sectionFilter !== 'all' && test.section !== sectionFilter) {
        return false;
      }

      // Status filter
      if (statusFilter !== 'all') {
        if (statusFilter === 'completed' && !test.grading_results) {
          return false;
        }
        if (statusFilter === 'in-progress' && test.grading_results) {
          return false;
        }
        if (statusFilter === 'free' && !test.free) {
          return false;
        }
        if (statusFilter === 'premium' && test.free) {
          return false;
        }
      }

      return true;
    });

    // Sort tests
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        case 'date-asc':
          return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
        case 'score-desc':
          return (b.score || 0) - (a.score || 0);
        case 'score-asc':
          return (a.score || 0) - (b.score || 0);
        case 'section':
          return a.section.localeCompare(b.section);
        case 'test-id':
          // Extract number from test ID for proper numerical sorting
          const getTestNumber = (testId: string) => {
            const match = testId.match(/(\d+)/);
            return match ? parseInt(match[1]) : 999999;
          };
          return getTestNumber(a.test_id) - getTestNumber(b.test_id);
        default:
          return 0;
      }
    });

    return filtered;
  }, [historyData?.history, searchTerm, sectionFilter, statusFilter, sortBy, t]);

  // Paginate tests
  const paginatedTests = useMemo(() => {
    const startIndex = (currentPage - 1) * testsPerPage;
    return filteredAndSortedTests.slice(startIndex, startIndex + testsPerPage);
  }, [filteredAndSortedTests, currentPage, testsPerPage]);

  const totalPages = Math.ceil(filteredAndSortedTests.length / testsPerPage);

  if (!user) {
    return (
      <Container size="lg" py="xl">
        <Alert
          variant="light"
          color="red"
          title={t('profile.unauthorized.title')}
          icon={<IconInfoCircle />}
        >
          {t('profile.unauthorized.message')}
        </Alert>
      </Container>
    );
  }

  const isMember = user.membership_type === 'premium' || user.membership_type === 'lifetime';
  const membershipExpired = isMember && isDateExpired(user.membership_expires_at);

  const renderCompactTestCard = (test: any) => {
    // Calculate progress from answers
    let progress = 0;
    let total = test.max_score || 39;
    
    if (test.answers) {
      try {
        const answers = typeof test.answers === 'string' ? JSON.parse(test.answers) : test.answers;
        if (test.test_id.startsWith('group')) {
          // For group tests, count answers 1 to total
          progress = Object.entries(answers).filter(([k, v]) => 
            v && v !== '' && k.match(/^\d+$/) && parseInt(k) >= 1 && parseInt(k) <= total
          ).length;
        } else {
          // For regular tests, count answers 1 to 39 only
          progress = Object.entries(answers).filter(([k, v]) => 
            v && v !== '' && k.match(/^\d+$/) && parseInt(k) >= 1 && parseInt(k) <= 39
          ).length;
          total = 39; // Ensure total is 39 for regular tests
        }
      } catch (e) {
        progress = test.score || 0;
      }
    } else if (test.score !== undefined) {
      progress = test.score;
    }

    const config = {
      listening: {
        title: t('home.sections.listening.title'),
        description: t('home.sections.listening.description'),
        color: '#007bff',
        icon: IconHeadphones
      },
      reading: {
        title: t('home.sections.reading.title'),
        description: t('home.sections.reading.description'),
        color: '#28a745',
        icon: IconBook
      },
      writing: {
        title: t('home.sections.writing.title'),
        description: t('home.sections.writing.description'),
        color: '#fd7e14',
        icon: IconPencil
      },
      speaking: {
        title: t('home.sections.speaking.title'),
        description: t('home.sections.speaking.description'),
        color: '#dc3545',
        icon: IconMicrophone
      }
    };
    const Icon = config[test.section as keyof typeof config]?.icon || IconBook;
    const color = config[test.section as keyof typeof config]?.color || '#007bff';
    
    const progressPercentage = progress && total ? (progress / total) * 100 : 0;
    const hasProgress = progress !== undefined && progress > 0;
    const isCompleted = test.status === 'completed' || progressPercentage === 100;
    
    const isGroupTest = test.test_id.startsWith('group');
    const navigationUrl = isGroupTest 
      ? `/${test.section}/group/${test.test_id.replace('group', '')}${test.free ? '?free=1' : ''}`
      : `/${test.section}/${test.test_id}${test.free ? '?free=1' : ''}`;

    // List view styled like ReadingListeningTests.tsx - removed "/39" display
    return (
      <Card 
        key={test.id} 
        shadow="sm" 
        padding="sm" 
        radius="md" 
        withBorder
        style={{
          background: themeColors.surface,
          borderColor: themeColors.border,
          color: themeColors.textPrimary,
          transition: 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          opacity: 1,
          position: 'relative'
        }}
      >
        {/* Icon in top left corner */}
        <Box style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 5 }}>
          <Icon size={20} color={color} />
        </Box>

        {/* Three dots menu in top right corner */}
        <Box style={{ position: 'absolute', top: '8px', right: '8px', zIndex: 10 }}>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <ActionIcon variant="light" color="gray" size="sm">
                <IconDots size={14} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item 
                color="red" 
                leftSection={<IconTrash size={14} />}
                onClick={() => handleDeleteTest(test)}
              >
                {t('profile.deleteHistory')}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Box>
        
        <Group justify="space-between" align="center" style={{ paddingTop: '28px', paddingRight: '32px' }}>
          <Group gap="sm" style={{ flex: 1 }}>
            <Box style={{ flex: 1 }}>
              <Group gap="xs" mb={2}>
                <Text fw={500} size="sm">
                  {formatTestTitle(test.section, test.test_id, t)}
                  {test.free && ' (Gratuit)'}
                </Text>
              </Group>
              <Text size="xs" c="dimmed">
                {formatDateWithUserTimezone(test.timestamp)}
              </Text>
            </Box>
          </Group>
          
          <Group gap="sm">
            {test.grading_results ? (
              <Group gap="xs">
                <Group gap="2px">
                  <IconCheck size={14} color="green" />
                  <Text size="xs" c="green">{test.correct_count || 0}</Text>
                </Group>
                <Group gap="2px">
                  <IconX size={14} color="red" />
                  <Text size="xs" c="red">{test.wrong_count || 0}</Text>
                </Group>
                <Badge size="sm" color="green" variant="light">
                  {test.score}
                </Badge>
              </Group>
            ) : test.score !== undefined ? (
              <Badge size="sm" color="blue" variant="light">
                {test.score}
              </Badge>
            ) : (
              <Text size="xs" c="dimmed">
                {progressPercentage.toFixed(0)}%
              </Text>
            )}
            
            <Button
              component={Link}
              to={navigationUrl}
              leftSection={<IconPlayerPlay size={12} />}
              size="xs"
              variant={test.grading_results ? 'outline' : 'filled'}
              color={test.free ? 'blue' : 'green'}
            >
              {test.grading_results ? t('profile.buttons.results') : isCompleted ? t('profile.buttons.review') : hasProgress ? t('profile.buttons.continue') : t('profile.buttons.start')}
            </Button>
          </Group>
        </Group>
      </Card>
    );
  };

  return (
    <Container size="lg" py="xl">
      <Title order={1} mb="xl">
        {t('profile.title')}
      </Title>

      <Grid>
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Stack gap="lg">
            {/* Account Information */}
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Group mb="md">
                <IconUser size={24} />
                <Title order={3}>{t('profile.accountInfo')}</Title>
              </Group>
              
              <Stack gap="sm">
                <Group>
                  <Text fw={500}>{t('profile.username')}:</Text>
                  <Text>{user.username}</Text>
                </Group>
                <Group>
                  <Text fw={500}>{t('profile.email')}:</Text>
                  <Text>{user.email}</Text>
                </Group>
              </Stack>

              <Divider my="md" />
              
              <Group>
                <Button
                  variant="outline"
                  leftSection={<IconKey size={16} />}
                  onClick={openChangePassword}
                >
                  {t('profile.passwordChange.button')}
                </Button>
                <Button
                  color="red"
                  variant="outline"
                  onClick={handleLogout}
                >
                  {t('profile.logout.button')}
                </Button>
              </Group>
            </Paper>

            {/* Membership Status */}
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Group mb="md">
                <IconCreditCard size={24} />
                <Title order={3}>{t('profile.membershipStatus')}</Title>
              </Group>
              
              <Stack gap="sm">
                <Group>
                  <Text fw={500}>{t('profile.statusLabel')}:</Text>
                  <Badge
                    color={isMember ? (membershipExpired ? 'red' : 'green') : 'gray'}
                    variant="light"
                  >
                    {isLifetimeMember() ? t('profile.status.lifetime') : 
                     isMember ? (membershipExpired ? t('profile.status.expired') : t('profile.status.active')) : t('profile.status.free')}
                  </Badge>
                </Group>
                
                {isMember && user.membership_expires_at && !isLifetimeMember() && (
                  <Group>
                    <Text fw={500}>{t('profile.expiresOn')}:</Text>
                    <Text c={membershipExpired ? 'red' : 'dimmed'}>
                      {formatDateWithUserTimezone(user.membership_expires_at)}
                      {!membershipExpired && user.membership_expires_at && (() => {
                        try {
                          const expiresDate = new Date(user.membership_expires_at);
                          const now = new Date();
                          const diffTime = expiresDate.getTime() - now.getTime();
                          
                          if (diffTime <= 0) return '';
                          
                          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
                          const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                          const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
                          
                          let timeText = '';
                          
                          // Show days if more than 0
                          if (diffDays > 0) {
                            const dayText = diffDays === 1 ? t('profile.progressText.daysLeft') : t('profile.progressText.daysLeftPlural');
                            timeText = `${diffDays} ${dayText}`;
                            
                            // Add hours if less than 7 days remaining
                            if (diffDays < 7) {
                              const hourText = diffHours === 1 ? t('profile.progressText.hoursLeft') : t('profile.progressText.hoursLeftPlural');
                              timeText += `, ${diffHours} ${hourText}`;
                            }
                          } else if (diffHours > 0) {
                            // Show only hours if less than 1 day
                            const hourText = diffHours === 1 ? t('profile.progressText.hoursLeft') : t('profile.progressText.hoursLeftPlural');
                            timeText = `${diffHours} ${hourText}`;
                            
                            // Add minutes if less than 24 hours remaining
                            const minuteText = diffMinutes === 1 ? t('profile.progressText.minutesLeft') : t('profile.progressText.minutesLeftPlural');
                            timeText += `, ${diffMinutes} ${minuteText}`;
                          } else {
                            // Show only minutes if less than 1 hour
                            const minuteText = diffMinutes === 1 ? t('profile.progressText.minutesLeft') : t('profile.progressText.minutesLeftPlural');
                            timeText = `${Math.max(1, diffMinutes)} ${minuteText}`;
                          }
                          
                          return ` (${timeText} ${t('profile.progressText.remaining')})`;
                        } catch {
                          return '';
                        }
                      })()}
                    </Text>
                  </Group>
                )}
                
                {isLifetimeMember() && (
                  <Group>
                    <Text fw={500}>{t('profile.type')}:</Text>
                    <Text c="green">
                      {t('profile.lifetimeAccess')}
                    </Text>
                  </Group>
                )}
              </Stack>

              <Divider my="md" />
              
              <Group>
                <Link to="/membership" style={{ textDecoration: 'none' }}>
                  <Button variant="outline">
                    {isMember ? t('profile.manageSubscription') : t('profile.subscribe')}
                  </Button>
                </Link>
                <Link to="/redeem-promo" style={{ textDecoration: 'none' }}>
                  <Button variant="light">
                    {t('profile.promoCode')}
                  </Button>
                </Link>
              </Group>
            </Paper>

            {/* Test History */}
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="sm">
                <IconHistory size={24} />
                <Title order={3}>{t('profile.testHistory')}</Title>
                  {filteredAndSortedTests.length > 0 && (
                    <Badge variant="light" color="blue">
                      {filteredAndSortedTests.length} test{filteredAndSortedTests.length > 1 ? 's' : ''}
                    </Badge>
                  )}
                </Group>
                

              </Group>
              
              {historyLoading ? (
                <Stack align="center" justify="center" py="xl">
                  <Loader size="lg" color="blue" />
                  <Text size="sm" c="dimmed" mt="xs">{t('loadingStates.history')}</Text>
                </Stack>
              ) : historyData?.history && historyData.history.length > 0 ? (
                <Stack gap="md">
                  {/* Filters and Search */}
                  <Grid>
                    <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                      <TextInput
                        placeholder={t('profile.filters.placeholders.search')}
                        leftSection={<IconSearch size={16} />}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        size="sm"
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 6, sm: 3, md: 2 }}>
                      <Select
                        placeholder={t('profile.filters.placeholders.section')}
                        value={sectionFilter}
                        onChange={(value) => setSectionFilter(value || 'all')}
                        data={[
                          { value: 'all', label: t('profile.filters.sections.all') },
                          { value: 'listening', label: t('profile.filters.sections.listening') },
                          { value: 'reading', label: t('profile.filters.sections.reading') },
                          { value: 'writing', label: t('profile.filters.sections.writing') },
                          { value: 'speaking', label: t('profile.filters.sections.speaking') },
                        ]}
                        size="sm"
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 6, sm: 3, md: 2 }}>
                      <Select
                        placeholder={t('profile.filters.placeholders.status')}
                        value={statusFilter}
                        onChange={(value) => setStatusFilter(value || 'all')}
                        data={[
                          { value: 'all', label: t('profile.filters.status.all') },
                          { value: 'completed', label: t('profile.filters.status.completed') },
                          { value: 'in-progress', label: t('profile.filters.status.inProgress') },
                          { value: 'free', label: t('profile.filters.status.free') },
                          { value: 'premium', label: t('profile.filters.status.premium') },
                        ]}
                        size="sm"
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                      <Select
                        placeholder={t('profile.filters.placeholders.sortBy')}
                        value={sortBy}
                        onChange={(value) => setSortBy(value || 'date-desc')}
                        data={[
                          { value: 'date-desc', label: t('profile.filters.sortBy.dateDesc') },
                          { value: 'date-asc', label: t('profile.filters.sortBy.dateAsc') },
                          { value: 'score-desc', label: t('profile.filters.sortBy.scoreDesc') },
                          { value: 'score-asc', label: t('profile.filters.sortBy.scoreAsc') },
                          { value: 'section', label: t('profile.filters.sortBy.section') },
                          { value: 'test-id', label: t('profile.filters.sortBy.testId') },
                        ]}
                        size="sm"
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6, md: 1 }}>
                      <Select
                        placeholder={t('profile.filters.placeholders.perPage')}
                        value={testsPerPage.toString()}
                        onChange={(value) => {
                          setTestsPerPage(parseInt(value || '12'));
                          setCurrentPage(1);
                        }}
                        data={[
                          { value: '6', label: '6' },
                          { value: '12', label: '12' },
                          { value: '24', label: '24' },
                          { value: '48', label: '48' },
                        ]}
                        size="sm"
                      />
                    </Grid.Col>
                  </Grid>

                  {/* Test Cards */}
                  {filteredAndSortedTests.length > 0 ? (
                    <Stack gap="md">
                      <Stack gap="xs">
                        {paginatedTests.map(renderCompactTestCard)}
                      </Stack>

                      {/* Pagination */}
                      {totalPages > 1 && (
                        <Group justify="center" mt="md">
                          <Pagination
                            value={currentPage}
                            onChange={setCurrentPage}
                            total={totalPages}
                            size="sm"
                            siblings={1}
                            boundaries={1}
                          />
                      </Group>
                      )}
                    </Stack>
                  ) : (
                    <Box style={{ textAlign: 'center', padding: '2rem' }}>
                      <Text c="dimmed" mb="md">
                        {t('profile.noTestsFound')}
                      </Text>
                      <Button 
                        variant="light"
                        onClick={() => {
                          setSearchTerm('');
                          setSectionFilter('all');
                          setStatusFilter('all');
                          setCurrentPage(1);
                        }}
                      >
                        {t('profile.resetFilters')}
                      </Button>
                    </Box>
                  )}
                </Stack>
              ) : (
                <Box style={{ textAlign: 'center', padding: '2rem' }}>
                  <Text c="dimmed" mb="md">
                  {t('profile.noTestsYet')}
                </Text>
                  <Button 
                    component={Link} 
                    to="/" 
                    variant="light"
                    leftSection={<IconPlayerPlay size={16} />}
                  >
                    {t('profile.takeFirstTest')}
                  </Button>
                </Box>
              )}
            </Paper>
          </Stack>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Title order={4} mb="md">{t('profile.quickActions')}</Title>
            <Stack gap="sm">
              <Link to="/" style={{ textDecoration: 'none' }}>
                <Button variant="light" fullWidth>
                  {t('profile.takeTest')}
                </Button>
              </Link>
              <Link to="/notebook" style={{ textDecoration: 'none' }}>
                <Button variant="light" fullWidth>
                  {t('profile.myNotes')}
                </Button>
              </Link>
            </Stack>
          </Paper>

          {/* Tools Section */}
          <Paper shadow="sm" p="lg" radius="md" withBorder mt="md">
            <Title order={4} mb="md">{t('profile.tools')}</Title>
            <Stack gap="sm">
              <Box>
                <Text size="sm" mb="xs" fw={500}>
                  {t('profile.translationMethod')}
                </Text>
                <SegmentedControl
                  value={translationMethod}
                  onChange={handleTranslationMethodChange}
                  data={[
                    {
                      label: 'Azure',
                      value: 'azure',
                      disabled: updateProfileMutation.isPending
                    },
                    {
                      label: 'GPT-4',
                      value: 'gpt4',
                      disabled: updateProfileMutation.isPending
                    }
                  ]}
                  fullWidth
                />
                <Text size="xs" c="dimmed" mt="xs">
                  {translationMethod === 'azure'
                    ? t('profile.azureDescription')
                    : t('profile.gpt4Description')
                  }
                </Text>
              </Box>
            </Stack>
          </Paper>
        </Grid.Col>
      </Grid>

      {/* Delete Test History Confirmation Modal */}
      <Modal 
        opened={deleteModalOpened} 
        onClose={closeDeleteModal} 
        title={t('profile.deleteTestHistory')}
        centered
      >
        <Stack gap="md">
          <Text>
            {t('profile.deleteWarning')}
          </Text>
          
          {testToDelete && (
            <Alert color="orange" variant="light">
              <Text size="sm">
                ⚠️ {t('profile.testLabel')}: {formatTestTitle(testToDelete.section, testToDelete.test_id, t)}
                {testToDelete.free && " (Gratuit)"}
                {testToDelete.score !== undefined && (
                  <span> - {t('profile.currentScore')}: {testToDelete.score}/{testToDelete.max_score || 39}</span>
                )}
              </Text>
            </Alert>
          )}
          
          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={closeDeleteModal}>
              {t('profile.cancel')}
            </Button>
            <Button 
              color="red" 
              onClick={() => deleteHistoryMutation.mutate()}
              loading={deleteHistoryMutation.isPending}
              leftSection={<IconTrash size={14} />}
            >
              {t('profile.deleteHistoryButton')}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Updated Change Password Modal with Email Verification */}
      <Modal
        opened={changePasswordOpened}
        onClose={handleClosePasswordModal}
        title={
          <Group gap="sm">
            <IconKey size={20} color="#007bff" />
            <Text fw={600}>{t('profile.passwordChange.title')}</Text>
          </Group>
        }
        centered
        size="md"
      >
        {!passwordResetSent ? (
          <Stack gap="md">
            <Alert color="blue" variant="light">
              <Group gap="xs">
                <IconShield size={16} />
                <Text size="sm">
                  {t('profile.passwordChange.securityNote')}
                </Text>
              </Group>
            </Alert>

            <Group>
              <IconMail size={16} color="#007bff" />
              <Text size="sm" fw={500}>{t('profile.passwordChange.emailAccount')}:</Text>
              <Text size="sm" c="dimmed">{user.email}</Text>
            </Group>

            <Alert color="orange" variant="light">
              <Text size="sm">
                💡 <strong>{t('profile.passwordChange.whyMethod')}</strong><br />
                {t('profile.passwordChange.securityExplanation')}
              </Text>
            </Alert>
            
            <Group justify="flex-end" gap="sm" mt="md">
              <Button variant="outline" onClick={handleClosePasswordModal} size="md">
                {t('profile.passwordChange.cancel')}
              </Button>
              <Button 
                onClick={handleRequestPasswordReset}
                loading={requestPasswordResetMutation.isPending}
                leftSection={<IconMail size={16} />}
                size="md"
              >
                {requestPasswordResetMutation.isPending ? t('profile.passwordChange.sending') : t('profile.passwordChange.sendLink')}
              </Button>
            </Group>
          </Stack>
        ) : (
          <Stack gap="md">
            <Alert color="green" variant="light">
              <Group gap="xs">
                <IconCheck size={16} />
                <Text size="sm" fw={500}>
                  {t('profile.passwordChange.successSent')}
                </Text>
              </Group>
            </Alert>

            <Text size="sm">
              {t('profile.passwordChange.resetSent')} <strong>{resetEmailAddress}</strong>.
            </Text>

            <Alert color="blue" variant="light">
              <Text size="sm">
                📧 {t('profile.passwordChange.checkInbox')}<br />
                ⏰ {t('profile.passwordChange.linkExpires')}
              </Text>
            </Alert>

            <Alert color="orange" variant="light">
              <Text size="sm">
                💡 <strong>{t('profile.passwordChange.troubleshoot')}</strong><br />
                {t('profile.passwordChange.checkSpam')}<br />
                {t('profile.passwordChange.waitTime')}<br />
                {t('profile.passwordChange.retryLater')}
              </Text>
            </Alert>
            
            <Group justify="center" mt="md">
              <Button onClick={handleClosePasswordModal} size="md" variant="light">
                {t('profile.passwordChange.close')}
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Container>
  );
} 