#!/usr/bin/env python3
"""
Debug Task Counts

This script helps debug the discrepancy in task counts between different sources.

Usage:
    python3 debug_task_counts.py --tache 1
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Set

class TaskCountDebugger:
    """Debug task count discrepancies."""
    
    def __init__(self):
        self.workspace_root = Path(__file__).parent.parent.parent.parent
        self.data_dir = self.workspace_root / 'data' / 'classified' / 'writing' / 'rule_based_classification'
        self.checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
    
    def load_latest_classification(self, tache_number: int) -> Dict[str, Any]:
        """Load the latest classification file."""
        classification_file = self.data_dir / f'tache_{tache_number}_classification.json'
        
        with open(classification_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def load_original_checkpoint(self, tache_number: int) -> Dict[str, Any]:
        """Load the original human-reviewed checkpoint."""
        pattern = f"tache_{tache_number}_final_review_*.json"
        checkpoints = [p for p in self.checkpoints_dir.glob(pattern)]
        
        if not checkpoints:
            raise FileNotFoundError(f"No original checkpoint found for Tâche {tache_number}")
        
        checkpoint_file = max(checkpoints, key=lambda p: p.stat().st_mtime)
        
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def extract_all_task_ids(self, data: Dict[str, Any], is_checkpoint: bool = False) -> Set[str]:
        """Extract all task IDs from classification or checkpoint data."""
        task_ids = set()
        
        if is_checkpoint:
            classification = data.get('classification', {})
            main_topics = classification.get('main_topics', {})
        else:
            main_topics = data.get('main_topics', {})
        
        for main_topic_data in main_topics.values():
            for subtopic_data in main_topic_data.get('subtopics', {}).values():
                # Handle task_entries format
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        if 'task_ids' in task_entry:
                            task_ids.update(task_entry['task_ids'])
                        elif 'representative_id' in task_entry:
                            task_ids.add(task_entry['representative_id'])
                
                # Handle tasks format
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        task_ids.add(task['id'])
        
        return task_ids
    
    def get_task_details(self, data: Dict[str, Any], task_ids: Set[str], is_checkpoint: bool = False) -> List[Dict[str, Any]]:
        """Get detailed information about specific tasks."""
        task_details = []
        
        if is_checkpoint:
            classification = data.get('classification', {})
            main_topics = classification.get('main_topics', {})
        else:
            main_topics = data.get('main_topics', {})
        
        for main_topic_name, main_topic_data in main_topics.items():
            for subtopic_name, subtopic_data in main_topic_data.get('subtopics', {}).items():
                # Handle task_entries format
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        entry_task_ids = task_entry.get('task_ids', [task_entry.get('representative_id')])
                        for task_id in entry_task_ids:
                            if task_id in task_ids:
                                task_details.append({
                                    'id': task_id,
                                    'content': task_entry.get('task_content', '')[:100] + "...",
                                    'location': f"{main_topic_name} > {subtopic_name}",
                                    'format': 'task_entries',
                                    'month_year': task_entry.get('month_years', [''])[0] if task_entry.get('month_years') else '',
                                    'combination': task_entry.get('combination_numbers', [''])[0] if task_entry.get('combination_numbers') else ''
                                })
                
                # Handle tasks format
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        if task['id'] in task_ids:
                            task_details.append({
                                'id': task['id'],
                                'content': task.get('task_content', '')[:100] + "...",
                                'location': f"{main_topic_name} > {subtopic_name}",
                                'format': 'tasks',
                                'month_year': task.get('month_year', ''),
                                'combination': task.get('combination_number', '')
                            })
        
        return task_details
    
    def debug_counts(self, tache_number: int):
        """Debug the task count discrepancy."""
        # Load data
        latest_classification = self.load_latest_classification(tache_number)
        original_checkpoint = self.load_original_checkpoint(tache_number)
        
        # Extract task IDs
        latest_task_ids = self.extract_all_task_ids(latest_classification, is_checkpoint=False)
        original_task_ids = self.extract_all_task_ids(original_checkpoint, is_checkpoint=True)
        
        # Find differences
        new_task_ids = latest_task_ids - original_task_ids
        missing_task_ids = original_task_ids - latest_task_ids
        
        print(f"\n📊 TASK COUNT ANALYSIS:")
        print(f"  Latest classification file: {len(latest_task_ids)} tasks")
        print(f"  Original checkpoint: {len(original_task_ids)} tasks")
        print(f"  New tasks (in latest, not in checkpoint): {len(new_task_ids)}")
        print(f"  Missing tasks (in checkpoint, not in latest): {len(missing_task_ids)}")
        
        # Show metadata from files
        print(f"\n📋 FILE METADATA:")
        print(f"  Latest classification total_tasks: {latest_classification.get('total_tasks', 'N/A')}")
        print(f"  Latest classification unique_tasks: {latest_classification.get('unique_tasks', 'N/A')}")
        
        checkpoint_classification = original_checkpoint.get('classification', {})
        print(f"  Original checkpoint total_tasks: {checkpoint_classification.get('total_tasks', 'N/A')}")
        print(f"  Original checkpoint unique_tasks: {checkpoint_classification.get('unique_tasks', 'N/A')}")
        
        if new_task_ids:
            print(f"\n📝 NEW TASKS ({len(new_task_ids)} found):")
            new_task_details = self.get_task_details(latest_classification, new_task_ids, is_checkpoint=False)
            for task in sorted(new_task_details, key=lambda x: x['id']):
                print(f"  • {task['id']} ({task['month_year']}, {task['combination']})")
                print(f"    {task['content']}")
                print(f"    Location: {task['location']}")
        
        if missing_task_ids:
            print(f"\n❌ MISSING TASKS ({len(missing_task_ids)} found):")
            missing_task_details = self.get_task_details(original_checkpoint, missing_task_ids, is_checkpoint=True)
            for task in sorted(missing_task_details, key=lambda x: x['id']):
                print(f"  • {task['id']} ({task['month_year']}, {task['combination']})")
                print(f"    {task['content']}")
                print(f"    Location: {task['location']}")
        
        print(f"\n🎯 CONCLUSION:")
        expected_new_tasks = len(latest_task_ids) - len(original_task_ids)
        actual_new_tasks = len(new_task_ids)
        
        if expected_new_tasks == actual_new_tasks:
            print(f"✅ Task count is correct: {actual_new_tasks} new tasks")
        else:
            print(f"⚠️  Task count discrepancy:")
            print(f"   Expected new tasks: {expected_new_tasks}")
            print(f"   Actual new tasks found: {actual_new_tasks}")
            print(f"   Missing tasks: {len(missing_task_ids)}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Debug task count discrepancies')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3], required=True,
                       help='Tâche number to debug (1, 2, or 3)')
    
    args = parser.parse_args()
    
    debugger = TaskCountDebugger()
    debugger.debug_counts(args.tache)


if __name__ == '__main__':
    main()
