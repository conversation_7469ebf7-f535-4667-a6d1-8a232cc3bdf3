#!/usr/bin/env python3
"""
Merge New Tasks into Human-Reviewed Checkpoints

This script automatically merges new monthly writing tasks from the latest classification
files into human-reviewed checkpoints. Similar tasks are merged into existing groups,
while new/different tasks go to manual review sections.

Features:
- Works with all three tâches (1, 2, 3)
- Compares latest classification with checkpoint to find new tasks
- Uses similarity matching to place new tasks
- Sends unmatched tasks to manual review
- Preserves checkpoint structure and human modifications
- Generates detailed merge reports

Usage:
    python3 merge_new_tasks.py --tache 1
    python3 merge_new_tasks.py --tache 2
    python3 merge_new_tasks.py --tache 3
    python3 merge_new_tasks.py --all
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Set, Tuple
import argparse
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_loader import load_tasks_for_tache

# Translation functionality
GOOGLE_TRANSLATE_AVAILABLE = False
OPENAI_TRANSLATE_AVAILABLE = False

try:
    from googletrans import Translator as GoogleTranslator
    GOOGLE_TRANSLATE_AVAILABLE = True
except ImportError:
    GOOGLE_TRANSLATE_AVAILABLE = False

try:
    from openai import OpenAI
    import os
    from pathlib import Path

    # Try to load OpenAI credentials from backend .env file
    backend_env_path = Path(__file__).parent.parent.parent.parent / 'web_react' / 'backend' / '.env'
    openai_key = None

    if backend_env_path.exists():
        with open(backend_env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('OPENAI_API_KEY='):
                    openai_key = line.split('=', 1)[1]

    # Also check environment variables as fallback
    openai_key = openai_key or os.getenv('OPENAI_API_KEY')

    if openai_key:
        OPENAI_TRANSLATE_AVAILABLE = True
    else:
        OPENAI_TRANSLATE_AVAILABLE = False
except ImportError:
    OPENAI_TRANSLATE_AVAILABLE = False

TRANSLATION_AVAILABLE = GOOGLE_TRANSLATE_AVAILABLE or OPENAI_TRANSLATE_AVAILABLE

class TopicTranslator:
    """Handles translation of topic and subtopic names using translation services."""

    def __init__(self):
        self.google_translator = None
        self.openai_client = None
        self.openai_key = None

        # Initialize Google Translator if available
        if GOOGLE_TRANSLATE_AVAILABLE:
            try:
                self.google_translator = GoogleTranslator()
                print("🌐 Google Translate service initialized")
            except Exception as e:
                print(f"⚠️  Failed to initialize Google Translator: {e}")
                self.google_translator = None

        # Initialize OpenAI if available
        if OPENAI_TRANSLATE_AVAILABLE:
            # Load OpenAI credentials from backend .env file
            backend_env_path = Path(__file__).parent.parent.parent.parent / 'web_react' / 'backend' / '.env'

            if backend_env_path.exists():
                with open(backend_env_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line.startswith('OPENAI_API_KEY='):
                            self.openai_key = line.split('=', 1)[1]

            # Fallback to environment variables
            import os
            self.openai_key = self.openai_key or os.getenv('OPENAI_API_KEY')

            if self.openai_key:
                try:
                    self.openai_client = OpenAI(api_key=self.openai_key)
                    print(f"🌐 OpenAI service initialized")
                except Exception as e:
                    print(f"⚠️  Failed to initialize OpenAI client: {e}")
                    self.openai_client = None

        if not TRANSLATION_AVAILABLE:
            pass  # Translation services not available, will use fallback formatting

    def translate_with_openai(self, text: str, target_lang: str) -> str:
        """Translate text using OpenAI."""
        if target_lang == 'en':
            # For English, convert from code format to human readable
            return self.make_human_readable(text)
            
        # Language mapping for prompts
        lang_prompts = {
            'fr': 'French',
            'zh': 'Chinese (Simplified)'
        }
        
        target_language_name = lang_prompts.get(target_lang, target_lang)
        
        prompt = f"""Translate the following English text to {target_language_name}. 
        The text is a topic or category name from an educational assessment. 
        Provide only the translation, no explanations.

        Text to translate: {text}"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional translator specializing in educational content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=100,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"⚠️  OpenAI translation failed: {e}")
            return text

    def make_human_readable(self, text: str) -> str:
        """Convert code-like topic names to human readable format using OpenAI."""
        if not self.openai_client:
            # Fallback: simple formatting
            return text.replace('_', ' ').replace('-', ' ').title()
            
        prompt = f"""Convert the following programming/code-style text into a natural, human-readable format suitable for educational topics. 
        Make it clear and readable while preserving the meaning.
        Provide only the converted text, no explanations.

        Text to convert: {text}"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert at converting technical identifiers into clear, human-readable labels."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=50,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"⚠️  OpenAI humanization failed: {e}")
            # Fallback: simple formatting
            return text.replace('_', ' ').replace('-', ' ').title()

    def translate_text(self, text: str, target_lang: str) -> str:
        """Translate text to target language using available translation service."""
        if not text:
            return text

        # For 'en', we want to convert to human readable even if it's already English
        if target_lang == 'en':
            if OPENAI_TRANSLATE_AVAILABLE and self.openai_client:
                try:
                    translated = self.make_human_readable(text)
                    return translated
                except Exception as e:
                    pass  # Fallback to simple formatting
            # Fallback: simple formatting
            return text.replace('_', ' ').replace('-', ' ').title()

        # Clean up the text for better translation
        clean_text = text.replace('_', ' ').replace('-', ' ').strip()

        # Try OpenAI first (better quality for educational content)
        if OPENAI_TRANSLATE_AVAILABLE and self.openai_client:
            try:
                translated = self.translate_with_openai(clean_text, target_lang)
                return translated
            except Exception as e:
                pass  # Fallback to Google Translate

        # Fallback to Google Translate
        if GOOGLE_TRANSLATE_AVAILABLE and self.google_translator:
            try:
                result = self.google_translator.translate(clean_text, src='en', dest=target_lang)
                translated = result.text
                return translated
            except Exception as e:
                pass  # Fallback to original text

        # Fallback: return original text if no translator available
        return text

    def add_translations_to_topic(self, topic_data: Dict[str, Any], topic_name: str) -> Dict[str, Any]:
        """Add translated names to a topic structure with four outputs: original name, name_en, name_fr, name_zh."""
        if not topic_data:
            return topic_data

        # Add all translation formats for topic names
        topic_data['original_name'] = topic_name  # Keep original code-like format
        topic_data['name_en'] = self.translate_text(topic_name, 'en')  # Human readable English
        topic_data['name_fr'] = self.translate_text(topic_name, 'fr')  # French translation
        topic_data['name_zh'] = self.translate_text(topic_name, 'zh')  # Chinese translation

        # Translate subtopics
        if 'subtopics' in topic_data:
            for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                subtopic_data['original_name'] = subtopic_name  # Keep original code-like format
                subtopic_data['name_en'] = self.translate_text(subtopic_name, 'en')  # Human readable English
                subtopic_data['name_fr'] = self.translate_text(subtopic_name, 'fr')  # French translation
                subtopic_data['name_zh'] = self.translate_text(subtopic_name, 'zh')  # Chinese translation

        return topic_data

class TaskMerger:
    """Merges new tasks from latest classifications into human-reviewed checkpoints."""

    def __init__(self):
        self.workspace_root = Path(__file__).parent.parent.parent.parent
        self.data_dir = self.workspace_root / 'data' / 'classified' / 'writing' / 'rule_based_classification'
        self.checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
        self.translator = TopicTranslator()
        
    def load_latest_classification(self, tache_number: int) -> Dict[str, Any]:
        """Load the latest classification file for a tâche."""
        classification_file = self.data_dir / f'tache_{tache_number}_classification.json'
        
        if not classification_file.exists():
            raise FileNotFoundError(f"Classification file not found: {classification_file}")
        
        with open(classification_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def load_checkpoint(self, tache_number: int) -> Dict[str, Any]:
        """Load the original human-reviewed checkpoint for a tâche (not merged ones)."""
        checkpoint_file = self.get_original_checkpoint_path(tache_number)

        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)

        print(f"📂 Using original checkpoint: {checkpoint_file.name}")
        return checkpoint_data
    
    def extract_task_ids_from_checkpoint(self, checkpoint: Dict[str, Any]) -> Set[str]:
        """Extract all task IDs that are already in the checkpoint."""
        task_ids = set()

        classification = checkpoint.get('classification', {})
        main_topics = classification.get('main_topics', {})

        for main_topic_data in main_topics.values():
            for subtopic_data in main_topic_data.get('subtopics', {}).values():
                # Handle both old 'tasks' format and new 'task_entries' format
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        if 'task_ids' in task_entry:
                            task_ids.update(task_entry['task_ids'])
                        elif 'representative_id' in task_entry:
                            task_ids.add(task_entry['representative_id'])

                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        task_ids.add(task['id'])

        return task_ids

    def get_original_checkpoint_path(self, tache_number: int) -> Path:
        """Get the path to the original human-reviewed checkpoint (not the merged one)."""
        # Look for checkpoints with 'final_review' in the name
        pattern = f"tache_{tache_number}_final_review_*.json"
        checkpoints = [p for p in self.checkpoints_dir.glob(pattern)]

        if checkpoints:
            # Get the most recent one
            return max(checkpoints, key=lambda p: p.stat().st_mtime)

        # If no final_review checkpoint found, try any non-merged checkpoint
        pattern = f"tache_{tache_number}_*.json"
        checkpoints = [p for p in self.checkpoints_dir.glob(pattern)
                      if not p.is_symlink() and 'merged' not in p.name]

        if checkpoints:
            return max(checkpoints, key=lambda p: p.stat().st_mtime)

        # If still nothing found, use the latest symlink
        latest_link = self.checkpoints_dir / f"tache_{tache_number}_latest.json"
        if latest_link.exists() and latest_link.is_symlink():
            return latest_link.resolve()

        raise FileNotFoundError(f"No checkpoints found for Tâche {tache_number}")
    
    def extract_new_tasks_from_classification(self, classification: Dict[str, Any], 
                                            existing_task_ids: Set[str]) -> List[Dict[str, Any]]:
        """Extract tasks from classification that are not in the checkpoint."""
        new_tasks = []
        
        main_topics = classification.get('main_topics', {})
        for main_topic_data in main_topics.values():
            for subtopic_data in main_topic_data.get('subtopics', {}).values():
                # Handle both old 'tasks' format and new 'task_entries' format
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        if 'task_ids' in task_entry:
                            # Check if any task in this group is new
                            has_new_task = any(tid not in existing_task_ids for tid in task_entry['task_ids'])
                            if has_new_task:
                                # Extract individual tasks from the group
                                for i, task_id in enumerate(task_entry['task_ids']):
                                    if task_id not in existing_task_ids:
                                        new_tasks.append({
                                            'id': task_id,
                                            'task_content': task_entry['task_content'],
                                            'month_year': task_entry['month_years'][i] if i < len(task_entry['month_years']) else '',
                                            'combination_number': task_entry['combination_numbers'][i] if i < len(task_entry['combination_numbers']) else ''
                                        })
                
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        if task['id'] not in existing_task_ids:
                            new_tasks.append(task)
        
        return new_tasks

    def _validate_merged_checkpoint(self, checkpoint: Dict[str, Any], new_tasks_added: int) -> None:
        """Validate the merged checkpoint for consistency."""
        classification = checkpoint['classification']

        # Count all tasks in the merged checkpoint
        total_task_entries = 0
        total_individual_tasks = 0
        calculated_total = 0
        calculated_unique = 0

        for main_topic_name, main_topic_info in classification['main_topics'].items():
            for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
                # Count task_entries
                if 'task_entries' in subtopic_info:
                    entries_count = len(subtopic_info['task_entries'])
                    total_task_entries += entries_count
                    # Sum up duplicate counts for total
                    for entry in subtopic_info['task_entries']:
                        calculated_total += entry.get('duplicate_count', 1)
                        calculated_unique += 1

                # Count individual tasks
                if 'tasks' in subtopic_info:
                    tasks_count = len(subtopic_info['tasks'])
                    total_individual_tasks += tasks_count
                    calculated_total += tasks_count
                    calculated_unique += tasks_count

        # Validate totals match
        reported_total = classification['total_tasks']
        reported_unique = classification['unique_tasks']

        print(f"📊 Validation Results:")
        print(f"  Task entries (original): {total_task_entries}")
        print(f"  Individual tasks (new): {total_individual_tasks}")
        print(f"  Calculated total: {calculated_total}")
        print(f"  Reported total: {reported_total}")
        print(f"  Calculated unique: {calculated_unique}")
        print(f"  Reported unique: {reported_unique}")

        # Check for discrepancies but don't treat them as errors since we use source totals
        if calculated_total != reported_total:
            print(f"ℹ️  INFO: Total task count difference - Calculated: {calculated_total}, Using source: {reported_total}")

        if calculated_unique != reported_unique:
            print(f"ℹ️  INFO: Unique task count difference - Calculated: {calculated_unique}, Using source: {reported_unique}")

        if total_individual_tasks != new_tasks_added:
            print(f"⚠️  WARNING: New tasks count mismatch! Expected: {new_tasks_added}, Found: {total_individual_tasks}")

        if total_individual_tasks == new_tasks_added:
            print("✅ New tasks count validated successfully!")

        print("✅ Checkpoint structure validated!")

    def add_translations_to_classification(self, classification: Dict[str, Any]) -> Dict[str, Any]:
        """Add translated topic and subtopic names to the classification data."""
        print("🌐 Adding translations to topic and subtopic names...")

        if 'main_topics' not in classification:
            return classification

        translated_count = 0
        for topic_name, topic_data in classification['main_topics'].items():
            # Add translations to the topic
            self.translator.add_translations_to_topic(topic_data, topic_name)
            translated_count += 1

            # Count subtopics for reporting
            if 'subtopics' in topic_data:
                translated_count += len(topic_data['subtopics'])

        print(f"✅ Added translations for {translated_count} topics and subtopics")
        return classification

    def merge_tasks_for_tache(self, tache_number: int, similarity_threshold: float = 0.15) -> Dict[str, Any]:
        """Merge new tasks for a specific tâche."""
        print(f"\n🔄 MERGING NEW TASKS FOR TÂCHE {tache_number}")
        print("=" * 60)
        
        # Load latest classification and checkpoint
        print("📂 Loading latest classification...")
        latest_classification = self.load_latest_classification(tache_number)

        print("📂 Loading human-reviewed checkpoint...")
        checkpoint = self.load_checkpoint(tache_number)

        # Store reference to latest classification for later use
        self.latest_classification = latest_classification
        
        # Find new tasks
        print("🔍 Identifying new tasks...")
        existing_task_ids = self.extract_task_ids_from_checkpoint(checkpoint)
        new_tasks = self.extract_new_tasks_from_classification(latest_classification, existing_task_ids)
        
        print(f"📊 Found {len(new_tasks)} new tasks to merge")
        
        if not new_tasks:
            print("✅ No new tasks to merge!")

            # Even if no new tasks, check if we need to update counts to match source
            source_total = self.latest_classification.get('total_tasks', 0)
            source_unique = self.latest_classification.get('unique_tasks', 0)
            checkpoint_total = checkpoint['classification'].get('total_tasks', 0)
            checkpoint_unique = checkpoint['classification'].get('unique_tasks', 0)

            if source_total != checkpoint_total or source_unique != checkpoint_unique:
                print(f"🔄 Updating checkpoint counts to match source file:")
                print(f"   Total tasks: {checkpoint_total} → {source_total}")
                print(f"   Unique tasks: {checkpoint_unique} → {source_unique}")

                # Update the checkpoint with correct counts
                checkpoint['classification']['total_tasks'] = source_total
                checkpoint['classification']['unique_tasks'] = source_unique

                # Add translations even when no new tasks
                checkpoint['classification'] = self.add_translations_to_classification(checkpoint['classification'])

                # Save the updated checkpoint back to the original file
                checkpoint_file_path = self.get_original_checkpoint_path(tache_number)
                with open(checkpoint_file_path, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint, f, ensure_ascii=False, indent=2)

                print(f"💾 Updated original checkpoint: {checkpoint_file_path.name}")

                return {
                    'success': True,
                    'new_tasks_count': 0,
                    'merged_tasks_count': 0,
                    'manual_review_count': 0,
                    'counts_updated': True,
                    'message': 'No new tasks found, but counts updated to match source'
                }
            else:
                # Even if counts are correct, still add translations if they're missing
                print("🌐 Checking if translations need to be added...")

                # Check if translations are already present and actually translated (not just copied English)
                needs_translation = False
                for topic_name, topic_data in checkpoint['classification'].get('main_topics', {}).items():
                    if ('name_fr' not in topic_data or 'name_zh' not in topic_data or
                        topic_data.get('name_fr') == topic_name or topic_data.get('name_zh') == topic_name):
                        needs_translation = True
                        break
                    # Also check subtopics
                    for subtopic_name, subtopic_data in topic_data.get('subtopics', {}).items():
                        if ('name_fr' not in subtopic_data or 'name_zh' not in subtopic_data or
                            subtopic_data.get('name_fr') == subtopic_name or subtopic_data.get('name_zh') == subtopic_name):
                            needs_translation = True
                            break
                    if needs_translation:
                        break

                if needs_translation:
                    print("🌐 Adding missing translations...")
                    checkpoint['classification'] = self.add_translations_to_classification(checkpoint['classification'])

                    # Save the updated checkpoint back to the original file
                    checkpoint_file_path = self.get_original_checkpoint_path(tache_number)
                    with open(checkpoint_file_path, 'w', encoding='utf-8') as f:
                        json.dump(checkpoint, f, ensure_ascii=False, indent=2)

                    print(f"💾 Updated original checkpoint with translations: {checkpoint_file_path.name}")

                    return {
                        'success': True,
                        'new_tasks_count': 0,
                        'merged_tasks_count': 0,
                        'manual_review_count': 0,
                        'counts_updated': False,
                        'translations_added': True,
                        'message': 'No new tasks found, but translations added'
                    }
                else:
                    print("✅ Translations already present")
                    return {
                        'success': True,
                        'new_tasks_count': 0,
                        'merged_tasks_count': 0,
                        'manual_review_count': 0,
                        'counts_updated': False,
                        'translations_added': False,
                        'message': 'No new tasks found, counts and translations already correct'
                    }
        
        # Use our own implementation to classify new tasks
        print("🧪 Classifying new tasks using checkpoint...")

        # Get the classification data
        classification = checkpoint.get('classification', {})
        if not classification:
            raise ValueError("No classification data found in checkpoint")

        # Initialize result structure based on checkpoint
        merge_results = {
            'metadata': {
                'method': 'checkpoint_based',
                'checkpoint_name': checkpoint['metadata']['checkpoint_name'],
                'tache_number': tache_number,
                'total_new_tasks': len(new_tasks),
                'similarity_threshold': similarity_threshold,
                'classified_timestamp': datetime.now().isoformat()
            },
            'main_topics': {}
        }

        # Copy checkpoint structure
        for main_topic_name, main_topic_info in classification['main_topics'].items():
            merge_results['main_topics'][main_topic_name] = {
                'topic_id': main_topic_info.get('topic_id', 0),
                'keywords': main_topic_info.get('keywords', []),
                'total_tasks': 0,
                'subtopics': {}
            }

            for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
                merge_results['main_topics'][main_topic_name]['subtopics'][subtopic_name] = {
                    'subtopic_id': subtopic_info.get('subtopic_id', 0),
                    'task_count': 0,
                    'task_ids': [],
                    'tasks': [],
                    'keywords': subtopic_info.get('keywords', []),
                    'template_similarity': subtopic_info.get('template_similarity', 1.0)
                }

        # Add unclassified category if not present
        if 'unclassified' not in merge_results['main_topics']:
            merge_results['main_topics']['unclassified'] = {
                'topic_id': len(merge_results['main_topics']),
                'keywords': [],
                'total_tasks': 0,
                'subtopics': {
                    'unknown': {
                        'subtopic_id': 0,
                        'task_count': 0,
                        'task_ids': [],
                        'tasks': [],
                        'keywords': [],
                        'template_similarity': 1.0
                    }
                }
            }

        # Add manual review category if not present
        if 'manual_review' not in merge_results['main_topics']:
            merge_results['main_topics']['manual_review'] = {
                'topic_id': len(merge_results['main_topics']),
                'keywords': [],
                'total_tasks': 0,
                'subtopics': {
                    'manual_review_general': {
                        'subtopic_id': 0,
                        'task_count': 0,
                        'task_ids': [],
                        'tasks': [],
                        'keywords': [],
                        'template_similarity': 1.0
                    }
                }
            }

        # Extract all task texts from checkpoint for similarity comparison
        reference_tasks = []
        task_to_location = {}

        # Process all tasks in the checkpoint
        for main_topic_name, main_topic_info in classification['main_topics'].items():
            for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
                # Handle both task_entries and tasks formats
                if 'task_entries' in subtopic_info:
                    for entry in subtopic_info['task_entries']:
                        task_text = entry.get('task_content', '')
                        if task_text:
                            task_id = entry.get('representative_id', '')
                            reference_tasks.append({
                                'id': task_id,
                                'text': task_text,
                                'main_topic': main_topic_name,
                                'subtopic': subtopic_name
                            })
                            task_to_location[task_text] = (main_topic_name, subtopic_name)

                if 'tasks' in subtopic_info:
                    for task in subtopic_info['tasks']:
                        task_text = task.get('task_content', '')
                        if task_text:
                            task_id = task.get('id', '')
                            reference_tasks.append({
                                'id': task_id,
                                'text': task_text,
                                'main_topic': main_topic_name,
                                'subtopic': subtopic_name
                            })
                            task_to_location[task_text] = (main_topic_name, subtopic_name)

        print(f"📊 Found {len(reference_tasks)} reference tasks in checkpoint")

        # Create vectorizer
        vectorizer = TfidfVectorizer(
            max_features=1000,
            ngram_range=(1, 2),
            lowercase=True,
            min_df=1
        )

        # Fit vectorizer on reference tasks
        reference_texts = [task['text'] for task in reference_tasks]
        reference_vectors = vectorizer.fit_transform(reference_texts)

        # Classification statistics
        classification_stats = {
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'unclassified': 0
        }

        # Classify each new task
        print(f"🔍 Classifying {len(new_tasks)} new tasks...")
        for task in new_tasks:
            task_text = task['task_content']
            task_vector = vectorizer.transform([task_text])

            # Calculate similarity with all reference tasks
            similarities = cosine_similarity(task_vector, reference_vectors)[0]

            # Find best match
            best_match_idx = similarities.argmax()
            best_similarity = similarities[best_match_idx]

            # Determine confidence level
            confidence = 'low'
            if best_similarity >= 0.5:
                confidence = 'high'
                classification_stats['high_confidence'] += 1
            elif best_similarity >= 0.3:
                confidence = 'medium'
                classification_stats['medium_confidence'] += 1
            elif best_similarity >= similarity_threshold:
                classification_stats['low_confidence'] += 1
            else:
                classification_stats['unclassified'] += 1

            # Get classification
            if best_similarity >= similarity_threshold:
                best_match = reference_tasks[best_match_idx]
                main_topic = best_match['main_topic']
                subtopic = best_match['subtopic']
            else:
                # No good match, send to manual review
                main_topic = 'manual_review'
                subtopic = 'manual_review_general'

            # Add task to appropriate subtopic
            merge_results['main_topics'][main_topic]['subtopics'][subtopic]['tasks'].append(task)
            merge_results['main_topics'][main_topic]['subtopics'][subtopic]['task_ids'].append(task['id'])
            merge_results['main_topics'][main_topic]['subtopics'][subtopic]['task_count'] += 1

        # Update counts
        for main_topic_name, main_topic_info in merge_results['main_topics'].items():
            total_tasks = 0
            for subtopic_info in main_topic_info['subtopics'].values():
                total_tasks += subtopic_info['task_count']
            main_topic_info['total_tasks'] = total_tasks
        
        # Count results
        merged_count = 0
        manual_review_count = 0

        for main_topic, main_info in merge_results['main_topics'].items():
            for subtopic, sub_info in main_info['subtopics'].items():
                task_count = sub_info['task_count']
                if main_topic == 'unclassified' or 'manual_review' in main_topic.lower():
                    manual_review_count += task_count
                else:
                    merged_count += task_count

        # Update the original checkpoint with merged tasks
        print("💾 Updating original checkpoint with merged tasks...")

        # Update the checkpoint metadata
        checkpoint['metadata']['description'] = f"Updated checkpoint with {len(new_tasks)} new tasks (auto-merged: {merged_count}, manual review: {manual_review_count})"
        checkpoint['metadata']['total_modifications'] = checkpoint['metadata'].get('total_modifications', 0) + len(new_tasks)

        # Add merge info to modifications log
        if 'modifications_log' not in checkpoint['metadata']:
            checkpoint['metadata']['modifications_log'] = []

        checkpoint['metadata']['modifications_log'].append({
            'action': 'merge_new_tasks',
            'new_tasks_added': len(new_tasks),
            'auto_merged_count': merged_count,
            'manual_review_count': manual_review_count,
            'similarity_threshold': similarity_threshold,
            'timestamp': datetime.now().isoformat()
        })

        # Use the original checkpoint as base
        merged_checkpoint = checkpoint

        # Update the original classification structure in place
        original_classification = merged_checkpoint['classification']

        # Update method to indicate new tasks were merged
        if '_with_new_tasks_merged' not in original_classification.get('method', ''):
            original_classification['method'] = f"{original_classification.get('method', 'unknown')}_with_new_tasks_merged"

        # Add new tasks to existing subtopics in the original classification
        for main_topic_name, main_topic_info in original_classification['main_topics'].items():
            for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
                # Add new tasks if any were classified to this subtopic
                new_tasks_for_subtopic = merge_results['main_topics'].get(main_topic_name, {}).get('subtopics', {}).get(subtopic_name, {}).get('tasks', [])
                if new_tasks_for_subtopic:
                    if 'tasks' not in subtopic_info:
                        subtopic_info['tasks'] = []

                    subtopic_info['tasks'].extend(new_tasks_for_subtopic)
                    subtopic_info['task_count'] = subtopic_info.get('task_count', 0) + len(new_tasks_for_subtopic)
                    subtopic_info['unique_task_count'] = subtopic_info.get('unique_task_count', 0) + len(new_tasks_for_subtopic)

                    # Update task_ids
                    if 'task_ids' not in subtopic_info:
                        subtopic_info['task_ids'] = []
                    new_task_ids = [task['id'] for task in new_tasks_for_subtopic]
                    subtopic_info['task_ids'].extend(new_task_ids)

        # Update main topic counts and calculate overall totals
        print("🔢 Recalculating all task counts after merge...")
        overall_total_tasks = 0
        overall_unique_tasks = 0

        for main_topic_name, main_topic_info in original_classification['main_topics'].items():
            topic_total_tasks = 0
            topic_unique_tasks = 0

            for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
                # Count tasks properly - avoid double counting
                subtopic_total_count = 0  # Total tasks (including duplicates)
                subtopic_unique_count = 0  # Unique tasks (representative tasks)

                # Count task_entries (original checkpoint tasks with deduplication)
                if 'task_entries' in subtopic_info:
                    for entry in subtopic_info['task_entries']:
                        # For total_tasks, count all task instances (duplicate_count)
                        duplicate_count = entry.get('duplicate_count', 1)
                        subtopic_total_count += duplicate_count
                        # For unique_tasks, count each entry as 1 representative task
                        subtopic_unique_count += 1

                # Count individual tasks (new merged tasks) - these are always individual
                if 'tasks' in subtopic_info:
                    individual_task_count = len(subtopic_info['tasks'])
                    subtopic_total_count += individual_task_count
                    subtopic_unique_count += individual_task_count

                # Update subtopic counts with correct terminology
                subtopic_info['task_count'] = subtopic_total_count  # Total including duplicates
                subtopic_info['unique_task_count'] = subtopic_unique_count  # Representative tasks

                topic_total_tasks += subtopic_total_count
                topic_unique_tasks += subtopic_unique_count

                if subtopic_total_count > 0:
                    print(f"  📊 {main_topic_name}.{subtopic_name}: {subtopic_total_count} total, {subtopic_unique_count} unique")

            # Update main topic counts
            main_topic_info['total_tasks'] = topic_total_tasks
            main_topic_info['unique_tasks'] = topic_unique_tasks

            overall_total_tasks += topic_total_tasks
            overall_unique_tasks += topic_unique_tasks

            if topic_total_tasks > 0:
                print(f"📈 {main_topic_name} total: {topic_total_tasks} total, {topic_unique_tasks} unique")

        # Set the correct overall totals - should match the source classification file
        # The merged result should have the same totals as the latest classification
        source_total_tasks = self.latest_classification.get('total_tasks', overall_total_tasks)
        source_unique_tasks = self.latest_classification.get('unique_tasks', overall_unique_tasks)

        print(f"📊 Calculated totals: {overall_total_tasks} total, {overall_unique_tasks} unique")
        print(f"📊 Source file totals: {source_total_tasks} total, {source_unique_tasks} unique")

        # Use source totals to ensure consistency with the latest classification
        original_classification['total_tasks'] = source_total_tasks
        original_classification['unique_tasks'] = source_unique_tasks

        if overall_total_tasks != source_total_tasks or overall_unique_tasks != source_unique_tasks:
            print(f"⚠️  NOTICE: Using source file totals instead of calculated totals for consistency")
            print(f"   This ensures the checkpoint matches the latest classification file")

        print(f"🎯 FINAL TOTALS: {source_total_tasks} total tasks, {source_unique_tasks} unique tasks")

        # Add translations to topic and subtopic names
        merged_checkpoint['classification'] = self.add_translations_to_classification(merged_checkpoint['classification'])

        # Validate the merged data before saving
        print("✅ Validating merged checkpoint...")
        self._validate_merged_checkpoint(merged_checkpoint, len(new_tasks))

        # Save back to the original checkpoint file (overwrite)
        original_checkpoint_path = self.get_original_checkpoint_path(tache_number)

        with open(original_checkpoint_path, 'w', encoding='utf-8') as f:
            json.dump(merged_checkpoint, f, ensure_ascii=False, indent=2)

        print(f"💾 Updated original checkpoint: {original_checkpoint_path.name}")

        output_path = original_checkpoint_path

        # Get original counts for comparison (before we modified them)
        original_total = checkpoint['classification'].get('total_tasks', 0)
        original_unique = checkpoint['classification'].get('unique_tasks', 0)
        new_total = original_classification['total_tasks']
        new_unique = original_classification['unique_tasks']

        # Summary information for logging
        summary = {
            'total_new_tasks': len(new_tasks),
            'auto_merged': merged_count,
            'manual_review': manual_review_count,
            'similarity_threshold': similarity_threshold,
            'total_tasks_change': new_total - original_total,
            'unique_tasks_change': new_unique - original_unique,
            'output_path': output_path
        }

        return {
            'success': True,
            'new_tasks_count': len(new_tasks),
            'merged_tasks_count': merged_count,
            'manual_review_count': manual_review_count,
            'output_file': str(output_path),
            'similarity_threshold': similarity_threshold,
            'original_total_tasks': original_total,
            'original_unique_tasks': original_unique,
            'new_total_tasks': new_total,
            'new_unique_tasks': new_unique
        }


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Merge new writing tasks into human-reviewed checkpoints')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3], 
                       help='Tâche number to process (1, 2, or 3)')
    parser.add_argument('--all', action='store_true', 
                       help='Process all tâches (1, 2, and 3)')
    parser.add_argument('--threshold', type=float, default=0.15,
                       help='Similarity threshold (default: 0.15)')
    
    args = parser.parse_args()
    
    if not args.tache and not args.all:
        parser.print_help()
        return
    
    merger = TaskMerger()
    
    # Determine which tâches to process
    taches_to_process = []
    if args.all:
        taches_to_process = [1, 2, 3]
    else:
        taches_to_process = [args.tache]
    
    # Process tasks silently unless there are errors
    
    overall_results = {}
    
    for tache_num in taches_to_process:
        try:
            result = merger.merge_tasks_for_tache(tache_num, args.threshold)
            overall_results[tache_num] = result
        except Exception as e:
            overall_results[tache_num] = {'success': False, 'error': str(e)}

    # Only print errors, success is silent
    for tache_num, result in overall_results.items():
        if not result['success']:
            print(f"Error processing Tâche {tache_num}: {result.get('error', 'Unknown error')}")
    
    if total_new > 0:
        print(f"\n🎯 TOTALS:")
        print(f"  New tasks found: {total_new}")
        print(f"  Auto-merged: {total_merged}")
        print(f"  Manual review needed: {total_manual}")
        print(f"  Auto-merge rate: {total_merged/total_new*100:.1f}%")


if __name__ == '__main__':
    main()
