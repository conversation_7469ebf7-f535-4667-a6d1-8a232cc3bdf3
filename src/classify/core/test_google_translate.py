#!/usr/bin/env python3
"""
Simple test for Google Translate functionality
"""

def test_google_translate():
    """Test Google Translate directly."""
    print("🧪 TESTING GOOGLE TRANSLATE")
    print("=" * 50)
    
    try:
        from googletrans import Translator
        print("✅ Google Translate imported successfully")
        
        translator = Translator()
        print("✅ Translator initialized")
        
        # Test translations
        test_topics = [
            'recommendation',
            'description places', 
            'description person',
            'narration',
            'opinion',
            'explanation',
            'argumentation',
            'travel blog',
            'food review'
        ]
        
        # Test translations silently
        for topic in test_topics:
            try:
                # Translate to French
                fr_result = translator.translate(topic, src='en', dest='fr')
                fr_translation = fr_result.text

                # Translate to Chinese
                zh_result = translator.translate(topic, src='en', dest='zh')
                zh_translation = zh_result.text

            except Exception as e:
                pass  # Silent failure for production

        return True
        
    except ImportError as e:
        return False
    except Exception as e:
        return False

if __name__ == '__main__':
    test_google_translate()
